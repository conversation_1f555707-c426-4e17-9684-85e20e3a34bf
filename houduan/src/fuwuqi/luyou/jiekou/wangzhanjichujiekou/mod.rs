#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

pub mod jiekou_wangzhanjichuxinxi;
pub mod jiekou_ziyuanhuoqu;

use crate::fuwuqi::luyou::jiekou_dingyii;
use rocket::Route;

/// 获取网站基础接口模块的所有路由
pub fn get_routes() -> Vec<Route> {
    let mut routes = Vec::new();
    routes.extend(jiekou_wangzhanjichuxinxi::jiekou_wangzhanjichuxinxi::get_routes());
    routes.extend(jiekou_ziyuanhuoqu::jiekou_ziyuanhuoqu::get_routes());
    routes
}

/// 获取网站基础接口模块的所有接口信息
pub fn get_jiekou_xinxi() -> Vec<(String, String, String, String)> {
    vec![
        jiekou_wangzhanjichuxinxi::jiekou_wangzhanjichuxinxi::get_jiekou_xinxi(),
        jiekou_ziyuanhuoqu::jiekou_ziyuanhuoqu::get_jiekou_xinxi()
    ]
}