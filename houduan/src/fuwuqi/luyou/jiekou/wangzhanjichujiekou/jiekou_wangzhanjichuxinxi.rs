#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use crate::dingyii_jiekou;
use crate::fuwuqi::luyou::luyoujiegouti_chuli::ming<PERSON>_xiangying;
use crate::fuwuqi::luyou::{jiekou_dingyii, luyou_qingqiu_kaishi, luyou_qingqiu_wancheng, luyou_rizhi_xinxi};
use crate::chushihua::peizhi::wangzhanjichuxinxi_guanli::wangzhanjichuxinxi_guanli;
use rocket::http::Status;
use rocket::serde::json::Json;
use rocket::{get, options, State};

/// 获取网站基础信息接口处理函数
#[get("/wangzhanjichuxinxi")]
pub fn huoqu_wangzhanjichuxinxi(
    wangzhan_guanli: &State<std::sync::Arc<wangzhanjichuxinxi_guanli>>,
) -> J<PERSON><mingwen_xiangying> {
    let jiekou_ming = jiekou_wangzhanjichuxinxi::get_miaoshu();
    let qingqiu_lujing = jiekou_wangzhanjichuxinxi::get_lujing();
    let qingqiu_fangfa = jiekou_wangzhanjichuxinxi::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 获取网站基础信息配置
    match wangzhan_guanli.wangzhanjichuxinxi_get_peizhi() {
        Ok(peizhi) => {
            // 将配置转换为JSON格式
            let peizhi_json = serde_json::to_value(&peizhi).unwrap_or_else(|_| {
                serde_json::json!({
                    "cuowu": "配置序列化失败"
                })
            });

            let xiangying = mingwen_xiangying::chenggong_with_shuju(
                "成功获取网站基础信息配置".to_string(),
                peizhi_json
            );

            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
            Json(xiangying)
        }
        Err(e) => {
            let cuowu_xiaoxi = format!("获取网站基础信息配置失败: {}", e);
            let xiangying = mingwen_xiangying::shibai_xiangying(cuowu_xiaoxi.clone());

            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            luyou_rizhi_xinxi(jiekou_ming, &cuowu_xiaoxi);
            Json(xiangying)
        }
    }
}

/// OPTIONS预检请求处理函数
#[options("/wangzhanjichuxinxi")]
pub fn huoqu_wangzhanjichuxinxi_yujian() -> Status {
    Status::Ok
}

// 使用宏定义接口
dingyii_jiekou!(
    jiekou_wangzhanjichuxinxi,
    lujing: "/wangzhanjichuxinxi",
    fangfa: "GET",
    miaoshu: "获取网站基础信息配置",
    jieshao: "获取网站的基础配置信息，包括网站名称、图标、导航菜单、SEO信息等",
    routes: [huoqu_wangzhanjichuxinxi, huoqu_wangzhanjichuxinxi_yujian]
);
