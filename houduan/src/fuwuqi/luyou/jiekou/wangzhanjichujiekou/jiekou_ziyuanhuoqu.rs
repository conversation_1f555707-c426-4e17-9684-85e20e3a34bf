#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use crate::dingyii_jiekou;
use crate::fuwuqi::luyou::{jiek<PERSON>_dingyii, luy<PERSON>_q<PERSON><PERSON><PERSON>_kaishi, luy<PERSON>_q<PERSON><PERSON><PERSON>_wancheng, luy<PERSON>_rizhi_xinxi};
use rocket::http::{Status, ContentType};
use rocket::{get, options, FromForm};
use std::path::{Path, PathBuf};
use tokio::fs;

/// 查询参数结构体
#[derive(FromForm)]
pub struct ziyuan_chaxun_canshu {
    moshi: Option<String>,
}

/// 自定义响应结构体
pub struct ziyuan_xiangying {
    pub content_type: ContentType,
    pub neirong: Vec<u8>,
    pub headers: Vec<(String, String)>,
}

impl<'r> rocket::response::Responder<'r, 'static> for ziyuan_xiangying {
    fn respond_to(self, _: &'r rocket::Request<'_>) -> rocket::response::Result<'static> {
        let mut response = rocket::Response::new();
        response.set_header(self.content_type);
        response.set_sized_body(self.neirong.len(), std::io::Cursor::new(self.neirong));

        for (key, value) in self.headers {
            response.set_raw_header(key, value);
        }

        Ok(response)
    }
}

/// 资源获取接口处理函数
#[get("/ziyuanhuoqu/<lujing..>?<chaxun_canshu..>")]
pub async fn huoqu_ziyuan(
    lujing: PathBuf,
    chaxun_canshu: Option<ziyuan_chaxun_canshu>
) -> Result<ziyuan_xiangying, Status> {
    let jiekou_ming = jiekou_ziyuanhuoqu::get_miaoshu();
    let qingqiu_lujing = format!("/ziyuanhuoqu/{}", lujing.display());
    let qingqiu_fangfa = jiekou_ziyuanhuoqu::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, &qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 确保ziyuan文件夹存在
    let ziyuan_mulu = Path::new("./ziyuan");
    if !ziyuan_mulu.exists() {
        if let Err(e) = fs::create_dir_all(ziyuan_mulu).await {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            luyou_rizhi_xinxi(jiekou_ming, &format!("创建ziyuan文件夹失败: {}", e));
            return Err(Status::InternalServerError);
        }
        luyou_rizhi_xinxi(jiekou_ming, "ziyuan文件夹不存在，已自动创建");
    }

    // 构建完整的文件路径
    let wenjian_lujing = ziyuan_mulu.join(&lujing);
    
    // 安全检查：防止路径遍历攻击
    if !wenjian_lujing.starts_with(ziyuan_mulu) {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 403);
        luyou_rizhi_xinxi(jiekou_ming, "非法路径访问被拒绝");
        return Err(Status::Forbidden);
    }

    // 检查文件是否存在
    if !wenjian_lujing.exists() {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 404);
        luyou_rizhi_xinxi(jiekou_ming, &format!("文件不存在: {}", wenjian_lujing.display()));
        return Err(Status::NotFound);
    }

    // 检查是否为文件（不是目录）
    if !wenjian_lujing.is_file() {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 403);
        luyou_rizhi_xinxi(jiekou_ming, "请求的路径不是文件");
        return Err(Status::Forbidden);
    }

    // 读取文件内容
    match fs::read(&wenjian_lujing).await {
        Ok(neirong) => {
            // 根据文件扩展名确定Content-Type
            let content_type = huoqu_content_type(&wenjian_lujing);

            // 获取文件名用于下载
            let wenjian_ming = wenjian_lujing
                .file_name()
                .and_then(|name| name.to_str())
                .unwrap_or("file");

            // 构建基础响应头
            let mut headers = vec![
                ("Access-Control-Allow-Origin".to_string(), "*".to_string()),
            ];

            // 根据模式参数决定响应类型
            if let Some(canshu) = chaxun_canshu {
                if let Some(moshi) = canshu.moshi {
                    match moshi.as_str() {
                        "xiazai" => {
                            // 下载模式：添加Content-Disposition头
                            headers.push((
                                "Content-Disposition".to_string(),
                                format!("attachment; filename=\"{}\"", wenjian_ming)
                            ));
                            luyou_rizhi_xinxi(jiekou_ming, "使用下载模式返回文件");
                        }
                        "shujuliu" => {
                            // 数据流模式：正常返回
                            luyou_rizhi_xinxi(jiekou_ming, "使用数据流模式返回文件");
                        }
                        _ => {
                            // 未知模式，使用默认模式
                            luyou_rizhi_xinxi(jiekou_ming, "未知模式参数，使用默认模式");
                        }
                    }
                }
            } else {
                // 无查询参数，使用默认模式
                luyou_rizhi_xinxi(jiekou_ming, "无查询参数，使用默认模式");
            }

            let xiangying = ziyuan_xiangying {
                content_type,
                neirong,
                headers,
            };

            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
            luyou_rizhi_xinxi(jiekou_ming, &format!("成功返回文件: {}", wenjian_lujing.display()));

            Ok(xiangying)
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            luyou_rizhi_xinxi(jiekou_ming, &format!("读取文件失败: {}", e));
            Err(Status::InternalServerError)
        }
    }
}

/// OPTIONS预检请求处理函数
#[options("/ziyuanhuoqu/<_lujing..>?<_chaxun_canshu..>")]
pub fn huoqu_ziyuan_yujian(
    _lujing: PathBuf,
    _chaxun_canshu: Option<ziyuan_chaxun_canshu>
) -> Status {
    Status::Ok
}

/// 根据文件扩展名获取Content-Type
fn huoqu_content_type(wenjian_lujing: &Path) -> ContentType {
    match wenjian_lujing.extension().and_then(|s| s.to_str()) {
        Some("png") => ContentType::PNG,
        Some("jpg") | Some("jpeg") => ContentType::JPEG,
        Some("gif") => ContentType::GIF,
        Some("webp") => ContentType::new("image", "webp"),
        Some("svg") => ContentType::SVG,
        Some("ico") => ContentType::Icon,
        Some("css") => ContentType::CSS,
        Some("js") => ContentType::JavaScript,
        Some("json") => ContentType::JSON,
        Some("xml") => ContentType::XML,
        Some("html") | Some("htm") => ContentType::HTML,
        Some("txt") => ContentType::Text,
        Some("pdf") => ContentType::PDF,
        Some("mp4") => ContentType::new("video", "mp4"),
        Some("mp3") => ContentType::new("audio", "mpeg"),
        Some("wav") => ContentType::new("audio", "wav"),
        Some("zip") => ContentType::new("application", "zip"),
        Some("rar") => ContentType::new("application", "x-rar-compressed"),
        Some("7z") => ContentType::new("application", "x-7z-compressed"),
        _ => ContentType::Binary,
    }
}

// 使用宏定义接口
dingyii_jiekou!(
    jiekou_ziyuanhuoqu,
    lujing: "/ziyuanhuoqu/<路径>",
    fangfa: "GET",
    miaoshu: "获取静态资源文件",
    jieshao: "从ziyuan文件夹中获取静态资源文件，支持图片、CSS、JS等多种文件类型，自动设置正确的Content-Type和缓存头",
    routes: [huoqu_ziyuan, huoqu_ziyuan_yujian]
);
