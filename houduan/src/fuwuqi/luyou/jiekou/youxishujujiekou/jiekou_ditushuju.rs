#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use crate::chushihua::shujukuxitong::redishujuku::redis_lianjie_guanli;
use crate::chushihua::shujukuxitong::youxishujuchuli::ditushuju::ditu_fenlei_chaxun::ditu_fenlei_chaxun;
use crate::chushihua::shujukuxitong::youxishujuchuli::ditushuju::ditu_lianhe_sousuo::ditu_lianhe_sousuo_guanli;
use crate::chushihua::shujukuxitong::youxishujuchuli::ditushuju::ditu_liebiao_chaxun::ditu_liebiao_chaxun;
use crate::chushihua::shujukuxitong::youxishujuchuli::ditushuju::ditu_mingzi_chaxun::ditu_mingzi_chaxun_guanli;
use crate::chushihua::shujukuxitong::youxishujuchuli::ditushuju::ditushujuchuli::ditushujuchuli;
use crate::chushihua::shujukuxitong::youxishujuchuli::ditushuju::ditushujujiegouti::{
    ditu_fenlei_chaxun_canshu, ditu_fenlei_tiaojian, ditu_lianhe_sousuo_canshu,
    ditu_liebiao_chaxun_canshu, ditu_mingzi_chaxun_tiaojian, ditu_mingzi_sousuo_leixing, ditu_mingzi_sousuo_tiaojian,
};
use crate::dingyii_jiekou;
use crate::fuwuqi::luyou::jiekou::youxishujujiekou::jiekou_ditushuju_rizhi::ditu_jiekou_rizhi_guanli;
use crate::fuwuqi::luyou::luyoujiegouti_chuli::mingwen_xiangying;
use crate::fuwuqi::luyou::{jiekou_dingyii, luyou_qingqiu_kaishi, luyou_qingqiu_wancheng, luyou_rizhi_xinxi};
use rocket::http::Status;
use rocket::serde::json::Json;
use rocket::{delete, get, options, State};
use std::sync::Arc;

fn chuangjian_ditu_guanli(
    mysql_guanli: &Arc<mysql_lianjie_guanli>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> ditushujuchuli {
    if let Some(redis_state) = redis_guanli {
        ditushujuchuli::new_with_redis(mysql_guanli.as_ref().clone(), redis_state.as_ref().clone())
    } else {
        ditushujuchuli::new(mysql_guanli.as_ref().clone())
    }
}

/// 辅助函数：创建地图列表查询管理器（带Redis缓存支持）
fn chuangjian_ditu_liebiao_chaxun_guanli(
    mysql_guanli: &Arc<mysql_lianjie_guanli>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> ditu_liebiao_chaxun {
    if let Some(redis_state) = redis_guanli {
        ditu_liebiao_chaxun::new_with_redis(mysql_guanli.as_ref().clone(), redis_state.as_ref().clone())
    } else {
        ditu_liebiao_chaxun::new(mysql_guanli.as_ref().clone())
    }
}

/// 辅助函数：创建地图分类查询管理器（带Redis缓存支持）
fn chuangjian_ditu_fenlei_chaxun_guanli(
    mysql_guanli: &Arc<mysql_lianjie_guanli>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> ditu_fenlei_chaxun {
    if let Some(redis_state) = redis_guanli {
        ditu_fenlei_chaxun::new_with_redis(mysql_guanli.as_ref().clone(), redis_state.as_ref().clone())
    } else {
        ditu_fenlei_chaxun::new(mysql_guanli.as_ref().clone())
    }
}

/// 辅助函数：创建地图名字查询管理器（带Redis缓存支持）
fn chuangjian_ditu_mingzi_chaxun_guanli(
    mysql_guanli: &Arc<mysql_lianjie_guanli>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> ditu_mingzi_chaxun_guanli {
    if let Some(redis_state) = redis_guanli {
        ditu_mingzi_chaxun_guanli::new_with_redis(mysql_guanli.as_ref().clone(), redis_state.as_ref().clone())
    } else {
        ditu_mingzi_chaxun_guanli::new(mysql_guanli.as_ref().clone())
    }
}

/// 辅助函数：创建地图联合搜索管理器（带Redis缓存支持）
fn chuangjian_ditu_lianhe_sousuo_guanli(
    mysql_guanli: &Arc<mysql_lianjie_guanli>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> ditu_lianhe_sousuo_guanli {
    if let Some(redis_state) = redis_guanli {
        ditu_lianhe_sousuo_guanli::new_with_redis(mysql_guanli.as_ref().clone(), redis_state.as_ref().clone())
    } else {
        ditu_lianhe_sousuo_guanli::new(mysql_guanli.as_ref().clone())
    }
}

#[get("/youxishuju/ditu/xinxi/<id>")]
pub async fn chaxun_ditu_quanbu_xinxi(
    id: String,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_ditu_quanbu_xinxi::get_miaoshu();
    let qingqiu_lujing = jiekou_ditu_quanbu_xinxi::get_lujing();
    let qingqiu_fangfa = jiekou_ditu_quanbu_xinxi::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    let ditu_guanli = chuangjian_ditu_guanli(mysql_guanli, redis_guanli);

    match ditu_guanli.huoqu_ditu_quanbu_xinxi(&id).await {
        Ok(Some(jieguo)) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::chenggong_with_shuju(
                ditu_jiekou_rizhi_guanli::chenggong_chaxun_ditu_quanbu_xinxi(&id),
                serde_json::to_value(&jieguo).unwrap_or_default(),
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
            luyou_rizhi_xinxi(jiekou_ming, &ditu_jiekou_rizhi_guanli::chenggong_chaxun_ditu_quanbu_xinxi(&id));
            Json(xiangying)
        }
        Ok(None) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                ditu_jiekou_rizhi_guanli::shibai_chaxun_ditu(&id)
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 404);
            Json(xiangying)
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                ditu_jiekou_rizhi_guanli::cuowu_chaxun_ditu_quanbu_xinxi(&id, &e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/ditu/xinxi/<_id>")]
pub fn chaxun_ditu_quanbu_xinxi_yujian(_id: String) -> Status {
    Status::Ok
}

#[get("/youxishuju/ditu/xinxi/<ziduan_ming>/<id>")]
pub async fn chaxun_ditu_ziduan_xinxi(
    ziduan_ming: String,
    id: String,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_ditu_ziduan_xinxi::get_miaoshu();
    let qingqiu_lujing = jiekou_ditu_ziduan_xinxi::get_lujing();
    let qingqiu_fangfa = jiekou_ditu_ziduan_xinxi::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // if !ditu_sql_kongzhi::jiancha_ziduan_hefa(&ziduan_ming) {
    //     let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
    //     let xiangying = mingwen_xiangying::shibai_xiangying(
    //         ditu_jiekou_rizhi_guanli::wuxiao_ziduan_ming(&ziduan_ming)
    //     );
    //     luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
    //     return Json(xiangying);
    // }

    let ditu_guanli = chuangjian_ditu_guanli(mysql_guanli, redis_guanli);

    match ditu_guanli.huoqu_ditu_quanbu_xinxi(&id).await {
        Ok(Some(jieguo)) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::chenggong_with_shuju(
                ditu_jiekou_rizhi_guanli::chenggong_chaxun_ditu_ziduan_xinxi(&id, &ziduan_ming),
                serde_json::to_value(&jieguo).unwrap_or_default(),
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
            luyou_rizhi_xinxi(jiekou_ming, &ditu_jiekou_rizhi_guanli::chenggong_chaxun_ditu_ziduan_xinxi(&id, &ziduan_ming));
            Json(xiangying)
        }
        Ok(None) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                ditu_jiekou_rizhi_guanli::shibai_chaxun_ditu(&id)
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 404);
            Json(xiangying)
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                ditu_jiekou_rizhi_guanli::cuowu_chaxun_ditu_ziduan_xinxi(&id, &ziduan_ming, &e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/ditu/xinxi/<_ziduan_ming>/<_id>")]
pub fn chaxun_ditu_ziduan_xinxi_yujian(_ziduan_ming: String, _id: String) -> Status {
    Status::Ok
}

#[delete("/youxishuju/ditu/huancun")]
pub async fn qingchu_suoyou_ditu_huancun(
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_qingchu_suoyou_ditu_huancun::get_miaoshu();
    let qingqiu_lujing = jiekou_qingchu_suoyou_ditu_huancun::get_lujing();
    let qingqiu_fangfa = jiekou_qingchu_suoyou_ditu_huancun::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    let ditu_guanli = chuangjian_ditu_guanli(mysql_guanli, redis_guanli);

    match ditu_guanli.qingchu_suoyou_ditu_huancun().await {
        Ok(shanchu_shuliang) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            if shanchu_shuliang > 0 {
                let xiangying = mingwen_xiangying::chenggong_with_shuju(
                    ditu_jiekou_rizhi_guanli::chenggong_qingchu_suoyou_ditu_huancun(shanchu_shuliang as u32),
                    serde_json::json!({"shanchu_shuliang": shanchu_shuliang}),
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                luyou_rizhi_xinxi(jiekou_ming, &ditu_jiekou_rizhi_guanli::chenggong_qingchu_suoyou_ditu_huancun(shanchu_shuliang as u32));
                Json(xiangying)
            } else {
                let xiangying = mingwen_xiangying::chenggong_xiangying(
                    ditu_jiekou_rizhi_guanli::meiyou_xuyao_qingchu_de_huancun(),
                    None,
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                Json(xiangying)
            }
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                ditu_jiekou_rizhi_guanli::cuowu_qingchu_suoyou_ditu_huancun(&e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/ditu/huancun")]
pub fn qingchu_suoyou_ditu_huancun_yujian() -> Status {
    Status::Ok
}

/// 查询地图列表接口（分页）
#[get("/youxishuju/ditu/liebiao?<yema>&<meiye_shuliang>")]
pub async fn chaxun_ditu_liebiao(
    yema: Option<u32>,
    meiye_shuliang: Option<u32>,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_ditu_liebiao::get_miaoshu();
    let qingqiu_lujing = jiekou_ditu_liebiao::get_lujing();
    let qingqiu_fangfa = jiekou_ditu_liebiao::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 设置默认分页参数
    let yema = yema.unwrap_or(1);
    let meiye_shuliang = meiye_shuliang.unwrap_or(10);

    // 验证分页参数
    if yema == 0 {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        let xiangying = mingwen_xiangying::shibai_xiangying(
            ditu_jiekou_rizhi_guanli::yema_buneng_wei_ling()
        );
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    if meiye_shuliang == 0 || meiye_shuliang > 100 {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        let xiangying = mingwen_xiangying::shibai_xiangying(
            ditu_jiekou_rizhi_guanli::meiye_shuliang_fanwei_cuowu()
        );
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    // 创建地图列表查询参数
    let chaxun_canshu = ditu_liebiao_chaxun_canshu::new(meiye_shuliang, yema);

    // 创建地图列表查询管理器
    let liebiao_guanli = chuangjian_ditu_liebiao_chaxun_guanli(mysql_guanli, redis_guanli);

    // 查询地图列表
    match liebiao_guanli.chaxun_ditu_liebiao(chaxun_canshu).await {
        Ok(jieguo) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::chenggong_with_shuju(
                ditu_jiekou_rizhi_guanli::chenggong_chaxun_ditu_liebiao(yema, jieguo.ditu_liebiao.len()),
                serde_json::to_value(&jieguo).unwrap_or_default(),
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
            luyou_rizhi_xinxi(jiekou_ming, &ditu_jiekou_rizhi_guanli::luyou_rizhi_chenggong_chaxun_ditu_liebiao(yema, meiye_shuliang));
            Json(xiangying)
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                ditu_jiekou_rizhi_guanli::cuowu_chaxun_ditu_liebiao(&e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/ditu/liebiao")]
pub fn chaxun_ditu_liebiao_yujian() -> Status {
    Status::Ok
}

/// 根据分类查询地图列表接口
#[get("/youxishuju/ditu/fenlei/<fenlei_ziduan>?<yema>&<meiye_shuliang>")]
pub async fn chaxun_ditu_by_fenlei(
    fenlei_ziduan: String,
    yema: Option<u32>,
    meiye_shuliang: Option<u32>,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_ditu_fenlei_liebiao::get_miaoshu();
    let qingqiu_lujing = jiekou_ditu_fenlei_liebiao::get_lujing();
    let qingqiu_fangfa = jiekou_ditu_fenlei_liebiao::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 设置默认分页参数
    let yema = yema.unwrap_or(1);
    let meiye_shuliang = meiye_shuliang.unwrap_or(10);

    // 验证分页参数
    if yema == 0 {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        let xiangying = mingwen_xiangying::shibai_xiangying(
            ditu_jiekou_rizhi_guanli::yema_buneng_wei_ling()
        );
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    if meiye_shuliang == 0 || meiye_shuliang > 100 {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        let xiangying = mingwen_xiangying::shibai_xiangying(
            ditu_jiekou_rizhi_guanli::meiye_shuliang_fanwei_cuowu()
        );
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    // 创建分类筛选条件
    let fenlei_tiaojian = vec![ditu_fenlei_tiaojian::new(fenlei_ziduan.clone(), true)];

    // 创建地图分类查询参数
    let chaxun_canshu = ditu_fenlei_chaxun_canshu::new(
        meiye_shuliang,
        yema,
        fenlei_tiaojian,
        "AND".to_string(),
    );

    // 创建地图分类查询管理器
    let fenlei_guanli = chuangjian_ditu_fenlei_chaxun_guanli(mysql_guanli, redis_guanli);

    // 查询分类地图列表
    match fenlei_guanli.chaxun_fenlei_ditu_liebiao(chaxun_canshu).await {
        Ok(jieguo) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::chenggong_with_shuju(
                ditu_jiekou_rizhi_guanli::chenggong_chaxun_ditu_fenlei(&fenlei_ziduan, yema, jieguo.ditu_liebiao.len()),
                serde_json::to_value(&jieguo).unwrap_or_default(),
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
            luyou_rizhi_xinxi(jiekou_ming, &ditu_jiekou_rizhi_guanli::luyou_rizhi_chenggong_chaxun_ditu_fenlei(&fenlei_ziduan, yema, meiye_shuliang));
            Json(xiangying)
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                ditu_jiekou_rizhi_guanli::cuowu_chaxun_ditu_fenlei(&fenlei_ziduan, &e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/ditu/fenlei/<_fenlei_ziduan>")]
pub fn chaxun_ditu_by_fenlei_yujian(_fenlei_ziduan: String) -> Status {
    Status::Ok
}

/// 清除地图列表缓存接口
#[delete("/youxishuju/ditu/liebiao/huancun")]
pub async fn qingchu_ditu_liebiao_huancun(
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_qingchu_ditu_liebiao_huancun::get_miaoshu();
    let qingqiu_lujing = jiekou_qingchu_ditu_liebiao_huancun::get_lujing();
    let qingqiu_fangfa = jiekou_qingchu_ditu_liebiao_huancun::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 创建地图列表查询管理器
    let liebiao_guanli = chuangjian_ditu_liebiao_chaxun_guanli(mysql_guanli, redis_guanli);

    // 清除地图列表缓存
    match liebiao_guanli.qingchu_ditu_liebiao_huancun().await {
        Ok(_) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::chenggong_xiangying(
                ditu_jiekou_rizhi_guanli::chenggong_qingchu_ditu_liebiao_huancun(),
                None,
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
            luyou_rizhi_xinxi(jiekou_ming, &ditu_jiekou_rizhi_guanli::luyou_rizhi_chenggong_qingchu_ditu_liebiao_huancun());
            Json(xiangying)
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                ditu_jiekou_rizhi_guanli::cuowu_qingchu_ditu_liebiao_huancun(&e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/ditu/liebiao/huancun")]
pub fn qingchu_ditu_liebiao_huancun_yujian() -> Status {
    Status::Ok
}

/// 清除地图分类查询缓存接口
#[delete("/youxishuju/ditu/fenlei/huancun")]
pub async fn qingchu_ditu_fenlei_huancun(
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_qingchu_ditu_fenlei_huancun::get_miaoshu();
    let qingqiu_lujing = jiekou_qingchu_ditu_fenlei_huancun::get_lujing();
    let qingqiu_fangfa = jiekou_qingchu_ditu_fenlei_huancun::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 创建地图分类查询管理器
    let fenlei_guanli = chuangjian_ditu_fenlei_chaxun_guanli(mysql_guanli, redis_guanli);

    // 清除分类查询缓存
    match fenlei_guanli.qingchu_fenlei_huancun().await {
        Ok(_) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::chenggong_xiangying(
                ditu_jiekou_rizhi_guanli::chenggong_qingchu_ditu_fenlei_huancun(),
                None,
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
            luyou_rizhi_xinxi(jiekou_ming, &ditu_jiekou_rizhi_guanli::luyou_rizhi_chenggong_qingchu_ditu_fenlei_huancun());
            Json(xiangying)
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                ditu_jiekou_rizhi_guanli::cuowu_qingchu_ditu_fenlei_huancun(&e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/ditu/fenlei/huancun")]
pub fn qingchu_ditu_fenlei_huancun_yujian() -> Status {
    Status::Ok
}

/// 获取支持的地图分类字段列表接口
#[get("/youxishuju/ditu/fenlei/ziduan")]
pub async fn huoqu_ditu_fenlei_ziduan_liebiao() -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_ditu_fenlei_ziduan_liebiao::get_miaoshu();
    let qingqiu_lujing = jiekou_ditu_fenlei_ziduan_liebiao::get_lujing();
    let qingqiu_fangfa = jiekou_ditu_fenlei_ziduan_liebiao::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 获取支持的分类字段列表
    let fenlei_ziduan_liebiao = vec![
        "leixing_town", "leixing_field", "leixing_dungeon", "leixing_quest",
        "leixing_instance", "leixing_siege", "leixing_pvp", "leixing_other", "leixing_weizhi",
        "fenlei_town", "fenlei_field", "fenlei_dungeon", "fenlei_quest",
        "fenlei_instance", "fenlei_siege", "fenlei_pvp", "fenlei_other", "fenlei_weizhi"
    ];

    let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
    let xiangying = mingwen_xiangying::chenggong_with_shuju(
        ditu_jiekou_rizhi_guanli::chenggong_huoqu_fenlei_ziduan(fenlei_ziduan_liebiao.len()),
        serde_json::json!({
            "fenlei_ziduan_liebiao": fenlei_ziduan_liebiao,
            "ziduan_shuliang": fenlei_ziduan_liebiao.len(),
            "shuoming": {
                "leixing": "地图类型分类，包括town(城镇)、field(野外)、dungeon(地下城)、quest(任务)、instance(副本)、siege(攻城)、pvp(PVP)、other(其他)、weizhi(未知)",
                "fenlei": "地图功能分类，字段含义与类型分类相同，但用于不同的分类维度"
            }
        }),
    );
    luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
    luyou_rizhi_xinxi(jiekou_ming, &ditu_jiekou_rizhi_guanli::luyou_rizhi_chenggong_huoqu_fenlei_ziduan());
    Json(xiangying)
}

#[options("/youxishuju/ditu/fenlei/ziduan")]
pub fn huoqu_ditu_fenlei_ziduan_liebiao_yujian() -> Status {
    Status::Ok
}

/// 根据地图ID精确查询地图列表接口
#[get("/youxishuju/ditu/id/<ditu_id>?<yema>&<meiye_shuliang>")]
pub async fn chaxun_ditu_by_id(
    ditu_id: String,
    yema: Option<u32>,
    meiye_shuliang: Option<u32>,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_ditu_id_chaxun::get_miaoshu();
    let qingqiu_lujing = jiekou_ditu_id_chaxun::get_lujing();
    let qingqiu_fangfa = jiekou_ditu_id_chaxun::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 设置默认分页参数
    let yema = yema.unwrap_or(1);
    let meiye_shuliang = meiye_shuliang.unwrap_or(10);

    // 验证分页参数
    if yema == 0 {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        let xiangying = mingwen_xiangying::shibai_xiangying(
            ditu_jiekou_rizhi_guanli::yema_buneng_wei_ling()
        );
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    if meiye_shuliang == 0 || meiye_shuliang > 100 {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        let xiangying = mingwen_xiangying::shibai_xiangying(
            ditu_jiekou_rizhi_guanli::meiye_shuliang_fanwei_cuowu()
        );
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    // 创建查询条件
    let fenye_canshu = ditu_liebiao_chaxun_canshu::new(meiye_shuliang, yema);
    let chaxun_tiaojian = ditu_mingzi_chaxun_tiaojian::ditu_id_jingque(ditu_id.clone(), fenye_canshu);

    // 创建地图名字查询管理器
    let mingzi_guanli = chuangjian_ditu_mingzi_chaxun_guanli(mysql_guanli, redis_guanli);

    // 执行查询
    match mingzi_guanli.tongyong_mingzi_chaxun(&chaxun_tiaojian).await {
        Ok(jieguo) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::chenggong_with_shuju(
                ditu_jiekou_rizhi_guanli::chenggong_chaxun_ditu_liebiao(yema, jieguo.ditu_liebiao.len()),
                serde_json::to_value(&jieguo).unwrap_or_default(),
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
            luyou_rizhi_xinxi(jiekou_ming, &format!("成功根据地图ID[{}]查询到{}个地图", ditu_id, jieguo.ditu_liebiao.len()));
            Json(xiangying)
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                ditu_jiekou_rizhi_guanli::cuowu_chaxun_ditu_liebiao(&e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/ditu/id/<_ditu_id>")]
pub fn chaxun_ditu_by_id_yujian(_ditu_id: String) -> Status {
    Status::Ok
}

/// 根据地图名字精确查询地图列表接口
#[get("/youxishuju/ditu/mingzi/<mingzi>?<yema>&<meiye_shuliang>")]
pub async fn chaxun_ditu_by_mingzi_jingque(
    mingzi: String,
    yema: Option<u32>,
    meiye_shuliang: Option<u32>,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_ditu_mingzi_jingque_chaxun::get_miaoshu();
    let qingqiu_lujing = jiekou_ditu_mingzi_jingque_chaxun::get_lujing();
    let qingqiu_fangfa = jiekou_ditu_mingzi_jingque_chaxun::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 设置默认分页参数
    let yema = yema.unwrap_or(1);
    let meiye_shuliang = meiye_shuliang.unwrap_or(10);

    // 验证分页参数
    if yema == 0 {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        let xiangying = mingwen_xiangying::shibai_xiangying(
            ditu_jiekou_rizhi_guanli::yema_buneng_wei_ling()
        );
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    if meiye_shuliang == 0 || meiye_shuliang > 100 {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        let xiangying = mingwen_xiangying::shibai_xiangying(
            ditu_jiekou_rizhi_guanli::meiye_shuliang_fanwei_cuowu()
        );
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    // 验证名字长度（至少2个字符）
    if mingzi.chars().count() < 2 {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        let xiangying = mingwen_xiangying::shibai_xiangying(
            "地图名字搜索至少需要2个字符".to_string()
        );
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    // 创建查询条件
    let fenye_canshu = ditu_liebiao_chaxun_canshu::new(meiye_shuliang, yema);
    let chaxun_tiaojian = ditu_mingzi_chaxun_tiaojian::mingzi_jingque(mingzi.clone(), fenye_canshu);

    // 创建地图名字查询管理器
    let mingzi_guanli = chuangjian_ditu_mingzi_chaxun_guanli(mysql_guanli, redis_guanli);

    // 执行查询
    match mingzi_guanli.tongyong_mingzi_chaxun(&chaxun_tiaojian).await {
        Ok(jieguo) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::chenggong_with_shuju(
                ditu_jiekou_rizhi_guanli::chenggong_chaxun_ditu_liebiao(yema, jieguo.ditu_liebiao.len()),
                serde_json::to_value(&jieguo).unwrap_or_default(),
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
            luyou_rizhi_xinxi(jiekou_ming, &format!("成功根据地图名字[{}]精确查询到{}个地图", mingzi, jieguo.ditu_liebiao.len()));
            Json(xiangying)
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                ditu_jiekou_rizhi_guanli::cuowu_chaxun_ditu_liebiao(&e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/ditu/mingzi/<_mingzi>")]
pub fn chaxun_ditu_by_mingzi_jingque_yujian(_mingzi: String) -> Status {
    Status::Ok
}

/// 根据地图名字模糊查询地图列表接口
#[get("/youxishuju/ditu/sousuo/<mingzi>?<yema>&<meiye_shuliang>")]
pub async fn chaxun_ditu_by_mingzi_mohu(
    mingzi: String,
    yema: Option<u32>,
    meiye_shuliang: Option<u32>,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_ditu_mingzi_mohu_chaxun::get_miaoshu();
    let qingqiu_lujing = jiekou_ditu_mingzi_mohu_chaxun::get_lujing();
    let qingqiu_fangfa = jiekou_ditu_mingzi_mohu_chaxun::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 设置默认分页参数
    let yema = yema.unwrap_or(1);
    let meiye_shuliang = meiye_shuliang.unwrap_or(10);

    // 验证分页参数
    if yema == 0 {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        let xiangying = mingwen_xiangying::shibai_xiangying(
            ditu_jiekou_rizhi_guanli::yema_buneng_wei_ling()
        );
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    if meiye_shuliang == 0 || meiye_shuliang > 100 {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        let xiangying = mingwen_xiangying::shibai_xiangying(
            ditu_jiekou_rizhi_guanli::meiye_shuliang_fanwei_cuowu()
        );
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    // 验证名字长度（至少2个字符）
    if mingzi.chars().count() < 2 {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        let xiangying = mingwen_xiangying::shibai_xiangying(
            "地图名字搜索至少需要2个字符".to_string()
        );
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    // 创建查询条件
    let fenye_canshu = ditu_liebiao_chaxun_canshu::new(meiye_shuliang, yema);
    let chaxun_tiaojian = ditu_mingzi_chaxun_tiaojian::mingzi_mohu(mingzi.clone(), fenye_canshu);

    // 创建地图名字查询管理器
    let mingzi_guanli = chuangjian_ditu_mingzi_chaxun_guanli(mysql_guanli, redis_guanli);

    // 执行查询
    match mingzi_guanli.tongyong_mingzi_chaxun(&chaxun_tiaojian).await {
        Ok(jieguo) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::chenggong_with_shuju(
                ditu_jiekou_rizhi_guanli::chenggong_chaxun_ditu_liebiao(yema, jieguo.ditu_liebiao.len()),
                serde_json::to_value(&jieguo).unwrap_or_default(),
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
            luyou_rizhi_xinxi(jiekou_ming, &format!("成功根据地图名字[{}]模糊查询到{}个地图", mingzi, jieguo.ditu_liebiao.len()));
            Json(xiangying)
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                ditu_jiekou_rizhi_guanli::cuowu_chaxun_ditu_liebiao(&e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/ditu/sousuo/<_mingzi>")]
pub fn chaxun_ditu_by_mingzi_mohu_yujian(_mingzi: String) -> Status {
    Status::Ok
}

/// 清除地图名字查询缓存接口
#[delete("/youxishuju/ditu/mingzi/huancun")]
pub async fn qingchu_ditu_mingzi_chaxun_huancun(
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_qingchu_ditu_mingzi_huancun::get_miaoshu();
    let qingqiu_lujing = jiekou_qingchu_ditu_mingzi_huancun::get_lujing();
    let qingqiu_fangfa = jiekou_qingchu_ditu_mingzi_huancun::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 创建地图名字查询管理器
    let mingzi_guanli = chuangjian_ditu_mingzi_chaxun_guanli(mysql_guanli, redis_guanli);

    // 清除名字查询缓存
    match mingzi_guanli.qingchu_mingzi_chaxun_huancun().await {
        Ok(qingchu_shuliang) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::chenggong_xiangying(
                format!("成功清除{}个地图名字查询缓存", qingchu_shuliang),
                Some(serde_json::json!({"qingchu_shuliang": qingchu_shuliang})),
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
            luyou_rizhi_xinxi(jiekou_ming, &format!("成功清除{}个地图名字查询缓存", qingchu_shuliang));
            Json(xiangying)
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                format!("清除地图名字查询缓存时发生错误: {}", e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/ditu/mingzi/huancun")]
pub fn qingchu_ditu_mingzi_chaxun_huancun_yujian() -> Status {
    Status::Ok
}

/// 地图联合搜索接口（支持分类+名字搜索）
#[get("/youxishuju/ditu/lianhe?<fenlei>&<mingzi>&<mingzi_leixing>&<yema>&<meiye_shuliang>")]
pub async fn ditu_lianhe_sousuo(
    fenlei: Option<String>,
    mingzi: Option<String>,
    mingzi_leixing: Option<String>,
    yema: Option<u32>,
    meiye_shuliang: Option<u32>,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_ditu_lianhe_sousuo::get_miaoshu();
    let qingqiu_lujing = jiekou_ditu_lianhe_sousuo::get_lujing();
    let qingqiu_fangfa = jiekou_ditu_lianhe_sousuo::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 设置默认分页参数
    let yema = yema.unwrap_or(1);
    let meiye_shuliang = meiye_shuliang.unwrap_or(10);

    // 验证分页参数
    if yema == 0 {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        let xiangying = mingwen_xiangying::shibai_xiangying(
            ditu_jiekou_rizhi_guanli::yema_buneng_wei_ling()
        );
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    if meiye_shuliang == 0 || meiye_shuliang > 100 {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        let xiangying = mingwen_xiangying::shibai_xiangying(
            ditu_jiekou_rizhi_guanli::meiye_shuliang_fanwei_cuowu()
        );
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    // 验证至少有一个搜索条件
    if fenlei.is_none() && mingzi.is_none() {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        let xiangying = mingwen_xiangying::shibai_xiangying(
            "联合搜索必须至少提供一个搜索条件（分类或名字）".to_string()
        );
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    // 创建名字搜索条件
    let mingzi_sousuo = if let Some(mingzi_zhi) = mingzi {
        // 验证名字长度（至少2个字符）
        if mingzi_zhi.chars().count() < 2 {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                "地图名字搜索至少需要2个字符".to_string()
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
            return Json(xiangying);
        }

        let sousuo_leixing = match mingzi_leixing.as_deref() {
            Some("jingque") => ditu_mingzi_sousuo_leixing::jingque,
            _ => ditu_mingzi_sousuo_leixing::mohu, // 默认模糊搜索
        };
        Some(ditu_mingzi_sousuo_tiaojian {
            mingzi: mingzi_zhi,
            sousuo_leixing,
        })
    } else {
        None
    };

    // 创建分页参数
    let fenye_canshu = ditu_liebiao_chaxun_canshu::new(meiye_shuliang, yema);

    // 创建联合搜索参数
    let sousuo_canshu = ditu_lianhe_sousuo_canshu {
        ditu_fenlei: fenlei.clone(),
        mingzi_sousuo,
        fenye_canshu,
    };

    // 创建地图联合搜索管理器
    let lianhe_guanli = chuangjian_ditu_lianhe_sousuo_guanli(mysql_guanli, redis_guanli);

    // 执行联合搜索
    match lianhe_guanli.lianhe_sousuo_ditu(&sousuo_canshu).await {
        Ok(jieguo) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::chenggong_with_shuju(
                format!("成功联合搜索到{}个地图", jieguo.ditu_liebiao.len()),
                serde_json::to_value(&jieguo).unwrap_or_default(),
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
            luyou_rizhi_xinxi(jiekou_ming, &format!("成功联合搜索 - 分类:{:?}, 名字:{:?}, 页码:{}, 结果:{}个",
                                                    fenlei, sousuo_canshu.mingzi_sousuo.as_ref().map(|m| &m.mingzi), yema, jieguo.ditu_liebiao.len()));
            Json(xiangying)
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                format!("联合搜索失败: {}", e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/ditu/lianhe")]
pub fn ditu_lianhe_sousuo_yujian() -> Status {
    Status::Ok
}

/// 清除地图联合搜索缓存接口
#[delete("/youxishuju/ditu/lianhe/huancun")]
pub async fn qingchu_ditu_lianhe_sousuo_huancun(
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_qingchu_ditu_lianhe_sousuo_huancun::get_miaoshu();
    let qingqiu_lujing = jiekou_qingchu_ditu_lianhe_sousuo_huancun::get_lujing();
    let qingqiu_fangfa = jiekou_qingchu_ditu_lianhe_sousuo_huancun::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 创建地图联合搜索管理器
    let lianhe_guanli = chuangjian_ditu_lianhe_sousuo_guanli(mysql_guanli, redis_guanli);

    // 清除联合搜索缓存
    match lianhe_guanli.qingchu_lianhe_sousuo_huancun().await {
        Ok(qingchu_shuliang) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::chenggong_xiangying(
                format!("成功清除{}个地图联合搜索缓存", qingchu_shuliang),
                Some(serde_json::json!({"qingchu_shuliang": qingchu_shuliang})),
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
            luyou_rizhi_xinxi(jiekou_ming, &format!("成功清除{}个地图联合搜索缓存", qingchu_shuliang));
            Json(xiangying)
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                format!("清除地图联合搜索缓存时发生错误: {}", e.to_string())
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/ditu/lianhe/huancun")]
pub fn qingchu_ditu_lianhe_sousuo_huancun_yujian() -> Status {
    Status::Ok
}

dingyii_jiekou!(
    jiekou_ditu_quanbu_xinxi,
    lujing: "/youxishuju/ditu/xinxi/{id}",
    fangfa: "GET",
    miaoshu: "查询地图全部信息",
    jieshao: "根据地图ID查询地图的全部信息",
    routes: [chaxun_ditu_quanbu_xinxi, chaxun_ditu_quanbu_xinxi_yujian]
);

dingyii_jiekou!(
    jiekou_ditu_ziduan_xinxi,
    lujing: "/youxishuju/ditu/xinxi/{字段名}/{id}",
    fangfa: "GET",
    miaoshu: "查询地图指定字段信息",
    jieshao: "根据字段名和地图ID查询地图的指定字段信息",
    routes: [chaxun_ditu_ziduan_xinxi, chaxun_ditu_ziduan_xinxi_yujian]
);

dingyii_jiekou!(
    jiekou_qingchu_suoyou_ditu_huancun,
    lujing: "/youxishuju/ditu/huancun",
    fangfa: "DELETE",
    miaoshu: "清理所有地图缓存",
    jieshao: "一键清理所有地图的Redis缓存",
    routes: [qingchu_suoyou_ditu_huancun, qingchu_suoyou_ditu_huancun_yujian]
);

// 使用宏定义地图列表查询接口
dingyii_jiekou!(
    jiekou_ditu_liebiao,
    lujing: "/youxishuju/ditu/liebiao?yema={页码}&meiye_shuliang={每页数量}",
    fangfa: "GET",
    miaoshu: "查询地图列表",
    jieshao: "分页查询地图列表，支持Redis缓存。页码从1开始，每页数量1-100",
    routes: [chaxun_ditu_liebiao, chaxun_ditu_liebiao_yujian]
);

// 使用宏定义地图分类查询接口
dingyii_jiekou!(
    jiekou_ditu_fenlei_liebiao,
    lujing: "/youxishuju/ditu/fenlei/{分类字段}?yema={页码}&meiye_shuliang={每页数量}",
    fangfa: "GET",
    miaoshu: "根据分类查询地图列表",
    jieshao: "根据分类字段查询地图列表，支持分页和Redis缓存。分类字段如：leixing_town(城镇类型)、fenlei_field(野外功能)等。页码从1开始，每页数量1-100",
    routes: [chaxun_ditu_by_fenlei, chaxun_ditu_by_fenlei_yujian]
);

// 使用宏定义清除地图列表缓存接口
dingyii_jiekou!(
    jiekou_qingchu_ditu_liebiao_huancun,
    lujing: "/youxishuju/ditu/liebiao/huancun",
    fangfa: "DELETE",
    miaoshu: "清除地图列表缓存",
    jieshao: "清除所有地图列表的Redis缓存，不影响地图详情缓存",
    routes: [qingchu_ditu_liebiao_huancun, qingchu_ditu_liebiao_huancun_yujian]
);

// 使用宏定义清除地图分类查询缓存接口
dingyii_jiekou!(
    jiekou_qingchu_ditu_fenlei_huancun,
    lujing: "/youxishuju/ditu/fenlei/huancun",
    fangfa: "DELETE",
    miaoshu: "清除地图分类查询缓存",
    jieshao: "清除所有地图分类查询的Redis缓存，不影响其他类型的缓存",
    routes: [qingchu_ditu_fenlei_huancun, qingchu_ditu_fenlei_huancun_yujian]
);

// 使用宏定义获取地图分类字段列表接口
dingyii_jiekou!(
    jiekou_ditu_fenlei_ziduan_liebiao,
    lujing: "/youxishuju/ditu/fenlei/ziduan",
    fangfa: "GET",
    miaoshu: "获取支持的地图分类字段列表",
    jieshao: "获取所有支持的地图分类字段列表，包括类型分类和功能分类两大类",
    routes: [huoqu_ditu_fenlei_ziduan_liebiao, huoqu_ditu_fenlei_ziduan_liebiao_yujian]
);

// 使用宏定义地图ID精确查询接口
dingyii_jiekou!(
    jiekou_ditu_id_chaxun,
    lujing: "/youxishuju/ditu/id/{地图ID}?yema={页码}&meiye_shuliang={每页数量}",
    fangfa: "GET",
    miaoshu: "根据地图ID精确查询地图列表",
    jieshao: "根据地图ID精确查询地图列表，支持分页和Redis缓存。页码从1开始，每页数量1-100",
    routes: [chaxun_ditu_by_id, chaxun_ditu_by_id_yujian]
);

// 使用宏定义地图名字精确查询接口
dingyii_jiekou!(
    jiekou_ditu_mingzi_jingque_chaxun,
    lujing: "/youxishuju/ditu/mingzi/{地图名字}?yema={页码}&meiye_shuliang={每页数量}",
    fangfa: "GET",
    miaoshu: "根据地图名字精确查询地图列表",
    jieshao: "根据地图名字精确查询地图列表，优先使用name表的名字，支持分页和Redis缓存。页码从1开始，每页数量1-100",
    routes: [chaxun_ditu_by_mingzi_jingque, chaxun_ditu_by_mingzi_jingque_yujian]
);

// 使用宏定义地图名字模糊查询接口
dingyii_jiekou!(
    jiekou_ditu_mingzi_mohu_chaxun,
    lujing: "/youxishuju/ditu/sousuo/{搜索关键字}?yema={页码}&meiye_shuliang={每页数量}",
    fangfa: "GET",
    miaoshu: "根据地图名字模糊查询地图列表",
    jieshao: "根据地图名字模糊查询地图列表，支持关键字搜索，优先使用name表的名字，支持分页和Redis缓存。页码从1开始，每页数量1-100",
    routes: [chaxun_ditu_by_mingzi_mohu, chaxun_ditu_by_mingzi_mohu_yujian]
);

// 使用宏定义清除地图名字查询缓存接口
dingyii_jiekou!(
    jiekou_qingchu_ditu_mingzi_huancun,
    lujing: "/youxishuju/ditu/mingzi/huancun",
    fangfa: "DELETE",
    miaoshu: "清除地图名字查询缓存",
    jieshao: "清除所有地图名字查询的Redis缓存，包括ID查询、精确查询和模糊查询的缓存",
    routes: [qingchu_ditu_mingzi_chaxun_huancun, qingchu_ditu_mingzi_chaxun_huancun_yujian]
);

// 使用宏定义地图联合搜索接口
dingyii_jiekou!(
    jiekou_ditu_lianhe_sousuo,
    lujing: "/youxishuju/ditu/lianhe?fenlei={分类}&mingzi={名字}&mingzi_leixing={搜索类型}&yema={页码}&meiye_shuliang={每页数量}",
    fangfa: "GET",
    miaoshu: "地图联合搜索",
    jieshao: "支持地图分类和名字的联合搜索，分类只能选择一个，名字支持精确(jingque)和模糊(mohu)搜索，支持分页和Redis缓存。页码从1开始，每页数量1-100",
    routes: [ditu_lianhe_sousuo, ditu_lianhe_sousuo_yujian]
);

// 使用宏定义清除地图联合搜索缓存接口
dingyii_jiekou!(
    jiekou_qingchu_ditu_lianhe_sousuo_huancun,
    lujing: "/youxishuju/ditu/lianhe/huancun",
    fangfa: "DELETE",
    miaoshu: "清除地图联合搜索缓存",
    jieshao: "清除所有地图联合搜索的Redis缓存，不影响其他类型的缓存",
    routes: [qingchu_ditu_lianhe_sousuo_huancun, qingchu_ditu_lianhe_sousuo_huancun_yujian]
);

pub fn get_routes() -> Vec<rocket::Route> {
    let mut routes = Vec::new();
    routes.extend(jiekou_ditu_quanbu_xinxi::get_routes());
    routes.extend(jiekou_ditu_ziduan_xinxi::get_routes());
    routes.extend(jiekou_qingchu_suoyou_ditu_huancun::get_routes());
    routes.extend(jiekou_ditu_liebiao::get_routes());
    routes.extend(jiekou_ditu_fenlei_liebiao::get_routes());
    routes.extend(jiekou_qingchu_ditu_liebiao_huancun::get_routes());
    routes.extend(jiekou_qingchu_ditu_fenlei_huancun::get_routes());
    routes.extend(jiekou_ditu_fenlei_ziduan_liebiao::get_routes());
    routes.extend(jiekou_ditu_id_chaxun::get_routes());
    routes.extend(jiekou_ditu_mingzi_jingque_chaxun::get_routes());
    routes.extend(jiekou_ditu_mingzi_mohu_chaxun::get_routes());
    routes.extend(jiekou_qingchu_ditu_mingzi_huancun::get_routes());
    routes.extend(jiekou_ditu_lianhe_sousuo::get_routes());
    routes.extend(jiekou_qingchu_ditu_lianhe_sousuo_huancun::get_routes());
    routes
}

pub fn get_jiekou_xinxi() -> Vec<(String, String, String, String)> {
    vec![
        jiekou_ditu_quanbu_xinxi::get_jiekou_xinxi(),
        jiekou_ditu_ziduan_xinxi::get_jiekou_xinxi(),
        jiekou_qingchu_suoyou_ditu_huancun::get_jiekou_xinxi(),
        jiekou_ditu_liebiao::get_jiekou_xinxi(),
        jiekou_ditu_fenlei_liebiao::get_jiekou_xinxi(),
        jiekou_qingchu_ditu_liebiao_huancun::get_jiekou_xinxi(),
        jiekou_qingchu_ditu_fenlei_huancun::get_jiekou_xinxi(),
        jiekou_ditu_fenlei_ziduan_liebiao::get_jiekou_xinxi(),
        jiekou_ditu_id_chaxun::get_jiekou_xinxi(),
        jiekou_ditu_mingzi_jingque_chaxun::get_jiekou_xinxi(),
        jiekou_ditu_mingzi_mohu_chaxun::get_jiekou_xinxi(),
        jiekou_qingchu_ditu_mingzi_huancun::get_jiekou_xinxi(),
        jiekou_ditu_lianhe_sousuo::get_jiekou_xinxi(),
        jiekou_qingchu_ditu_lianhe_sousuo_huancun::get_jiekou_xinxi(),
    ]
}