#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 地图单个字段查询结果
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ditu_ziduan_jieguo {
    /// 地图ID
    pub ditu_id: String,
    /// 字段名
    pub ziduan_ming: String,
    /// 字段值
    pub ziduan_zhi: Option<String>,
}

/// 地图全部信息查询结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ditu_quanbu_xinxi_jieguo {
    /// 地图ID
    pub ditu_id: String,
    /// 所有字段数据（包含huizong表和name表的合并数据）
    pub suoyou_ziduan: HashMap<String, String>,
    /// 数据来源（"缓存" 或 "数据库"）
    pub shuju_laiyuan: String,
}

/// 地图缓存操作结果
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ditu_huancun_caozuo_jieguo {
    /// 地图ID
    pub ditu_id: String,
    /// 操作是否成功
    pub chenggong: bool,
    /// 操作消息
    pub xiaoxi: String,
}

/// 地图数据查询参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ditu_chaxun_canshu {
    /// 地图ID
    pub ditu_id: String,
    /// 字段名（如果是"quanbu_xinxi"则获取全部信息）
    pub ziduan_ming: String,
}

impl ditu_ziduan_jieguo {
    /// 创建新的地图字段查询结果
    pub fn new(ditu_id: String, ziduan_ming: String, ziduan_zhi: Option<String>) -> Self {
        Self {
            ditu_id,
            ziduan_ming,
            ziduan_zhi,
        }
    }

    /// 检查字段值是否存在
    pub fn you_zhi(&self) -> bool {
        self.ziduan_zhi.is_some()
    }
}

impl ditu_quanbu_xinxi_jieguo {
    /// 创建新的地图全部信息查询结果
    pub fn new(ditu_id: String, suoyou_ziduan: HashMap<String, String>, shuju_laiyuan: String) -> Self {
        Self {
            ditu_id,
            suoyou_ziduan,
            shuju_laiyuan,
        }
    }

    /// 获取指定字段的值
    pub fn huoqu_ziduan_zhi(&self, ziduan_ming: &str) -> Option<&String> {
        self.suoyou_ziduan.get(ziduan_ming)
    }

    /// 获取字段数量
    pub fn ziduan_shuliang(&self) -> usize {
        self.suoyou_ziduan.len()
    }

    /// 检查是否来自缓存
    pub fn laizi_huancun(&self) -> bool {
        self.shuju_laiyuan == "缓存"
    }
}

impl ditu_huancun_caozuo_jieguo {
    /// 创建成功的缓存操作结果
    pub fn chenggong(ditu_id: String, xiaoxi: String) -> Self {
        Self {
            ditu_id,
            chenggong: true,
            xiaoxi,
        }
    }

    /// 创建失败的缓存操作结果
    pub fn shibai(ditu_id: String, xiaoxi: String) -> Self {
        Self {
            ditu_id,
            chenggong: false,
            xiaoxi,
        }
    }
}

impl ditu_chaxun_canshu {
    /// 创建新的地图查询参数
    pub fn new(ditu_id: String, ziduan_ming: String) -> Self {
        Self {
            ditu_id,
            ziduan_ming,
        }
    }

    /// 检查是否是获取全部信息的查询
    pub fn shi_quanbu_xinxi_chaxun(&self) -> bool {
        self.ziduan_ming == "quanbu_xinxi"
    }
}

/// 地图列表项
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ditu_liebiao_xiangmu {
    /// 地图ID
    pub ditu_id: String,
    /// 地图名字
    pub ditu_mingzi: String,
    /// 地图分类（包含所有为true的分类字段）
    pub ditu_fenlei: HashMap<String, bool>,
}

/// 地图列表查询参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ditu_liebiao_chaxun_canshu {
    /// 每页数量
    pub mei_ye_shuliang: u32,
    /// 当前页数（从1开始）
    pub dangqian_ye: u32,
}

/// 地图列表查询结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ditu_liebiao_jieguo {
    /// 查询是否成功
    pub chenggong: bool,
    /// 错误信息
    pub cuowu_xinxi: Option<String>,
    /// 地图列表
    pub ditu_liebiao: Vec<ditu_liebiao_xiangmu>,
    /// 当前页数
    pub dangqian_ye: u32,
    /// 全部页数
    pub quanbu_ye_shu: u32,
    /// 总共地图数量
    pub zonggong_ditu_shuliang: u32,
    /// 当前页面地图数量
    pub dangqian_ye_ditu_shuliang: u32,
    /// 数据来源（mysql/redis）
    pub shuju_laiyuan: Option<String>,
}

impl ditu_liebiao_xiangmu {
    /// 创建新的地图列表项
    pub fn new(ditu_id: String, ditu_mingzi: String, ditu_fenlei: HashMap<String, bool>) -> Self {
        Self {
            ditu_id,
            ditu_mingzi,
            ditu_fenlei,
        }
    }
}

impl ditu_liebiao_chaxun_canshu {
    /// 创建新的地图列表查询参数
    pub fn new(mei_ye_shuliang: u32, dangqian_ye: u32) -> Self {
        Self {
            mei_ye_shuliang,
            dangqian_ye,
        }
    }

    /// 计算OFFSET值
    pub fn jisuan_offset(&self) -> u32 {
        (self.dangqian_ye - 1) * self.mei_ye_shuliang
    }
}

impl ditu_liebiao_jieguo {
    /// 创建成功的地图列表查询结果
    pub fn new(
        ditu_liebiao: Vec<ditu_liebiao_xiangmu>,
        dangqian_ye: u32,
        zonggong_ditu_shuliang: u32,
        mei_ye_shuliang: u32,
    ) -> Self {
        let dangqian_ye_ditu_shuliang = ditu_liebiao.len() as u32;
        let quanbu_ye_shu = if zonggong_ditu_shuliang == 0 {
            0
        } else {
            (zonggong_ditu_shuliang + mei_ye_shuliang - 1) / mei_ye_shuliang
        };

        Self {
            chenggong: true,
            cuowu_xinxi: None,
            ditu_liebiao,
            dangqian_ye,
            quanbu_ye_shu,
            zonggong_ditu_shuliang,
            dangqian_ye_ditu_shuliang,
            shuju_laiyuan: Some("mysql".to_string()),
        }
    }

    /// 创建成功的地图列表查询结果（带数据来源）
    pub fn chenggong_with_laiyuan(
        ditu_liebiao: Vec<ditu_liebiao_xiangmu>,
        dangqian_ye: u32,
        zonggong_ditu_shuliang: u32,
        mei_ye_shuliang: u32,
        shuju_laiyuan: String,
    ) -> Self {
        let dangqian_ye_ditu_shuliang = ditu_liebiao.len() as u32;
        let quanbu_ye_shu = if zonggong_ditu_shuliang == 0 {
            0
        } else {
            (zonggong_ditu_shuliang + mei_ye_shuliang - 1) / mei_ye_shuliang
        };

        Self {
            chenggong: true,
            cuowu_xinxi: None,
            ditu_liebiao,
            dangqian_ye,
            quanbu_ye_shu,
            zonggong_ditu_shuliang,
            dangqian_ye_ditu_shuliang,
            shuju_laiyuan: Some(shuju_laiyuan),
        }
    }

    /// 创建失败的地图列表查询结果
    pub fn shibai(cuowu_xinxi: String) -> Self {
        Self {
            chenggong: false,
            cuowu_xinxi: Some(cuowu_xinxi),
            ditu_liebiao: Vec::new(),
            dangqian_ye: 1,
            quanbu_ye_shu: 0,
            zonggong_ditu_shuliang: 0,
            dangqian_ye_ditu_shuliang: 0,
            shuju_laiyuan: None,
        }
    }
}

/// 地图分类筛选条件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ditu_fenlei_tiaojian {
    /// 字段名（如"leixing_town", "fenlei_field"等）
    pub ziduan_ming: String,
    /// 期望的值（true表示该分类为true）
    pub zhi: bool,
}

/// 地图分类查询参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ditu_fenlei_chaxun_canshu {
    /// 每页数量
    pub mei_ye_shuliang: u32,
    /// 当前页数（从1开始）
    pub dangqian_ye: u32,
    /// 分类筛选条件列表
    pub fenlei_tiaojian: Vec<ditu_fenlei_tiaojian>,
    /// 多个条件之间的逻辑关系（"AND" 或 "OR"）
    pub luoji_guanxi: String,
}

impl ditu_fenlei_tiaojian {
    /// 创建新的分类筛选条件
    pub fn new(ziduan_ming: String, zhi: bool) -> Self {
        Self {
            ziduan_ming,
            zhi,
        }
    }
}

impl ditu_fenlei_chaxun_canshu {
    /// 创建新的地图分类查询参数
    pub fn new(mei_ye_shuliang: u32, dangqian_ye: u32, fenlei_tiaojian: Vec<ditu_fenlei_tiaojian>, luoji_guanxi: String) -> Self {
        Self {
            mei_ye_shuliang,
            dangqian_ye,
            fenlei_tiaojian,
            luoji_guanxi,
        }
    }

    /// 计算OFFSET值
    pub fn jisuan_offset(&self) -> u32 {
        (self.dangqian_ye - 1) * self.mei_ye_shuliang
    }

    /// 检查是否有筛选条件
    pub fn you_tiaojian(&self) -> bool {
        !self.fenlei_tiaojian.is_empty()
    }

    /// 生成条件的哈希值（用于缓存键）
    pub fn shengcheng_tiaojian_hash(&self) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();

        // 对条件进行排序以确保相同条件组合产生相同hash
        let mut sorted_tiaojian = self.fenlei_tiaojian.clone();
        sorted_tiaojian.sort_by(|a, b| a.ziduan_ming.cmp(&b.ziduan_ming));

        for tiaojian in &sorted_tiaojian {
            tiaojian.ziduan_ming.hash(&mut hasher);
            tiaojian.zhi.hash(&mut hasher);
        }
        self.luoji_guanxi.hash(&mut hasher);

        format!("{:x}", hasher.finish())
    }
}

/// 地图名字查询类型枚举
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ditu_mingzi_chaxun_leixing {
    /// 地图ID精确查询
    ditu_id_jingque,
    /// 名字精确查询
    mingzi_jingque,
    /// 名字模糊查询
    mingzi_mohu,
}

impl ditu_mingzi_chaxun_leixing {
    /// 转换为字符串标识
    pub fn to_string(&self) -> String {
        match self {
            Self::ditu_id_jingque => "ditu_id_jingque".to_string(),
            Self::mingzi_jingque => "mingzi_jingque".to_string(),
            Self::mingzi_mohu => "mingzi_mohu".to_string(),
        }
    }
}

/// 地图名字查询条件结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ditu_mingzi_chaxun_tiaojian {
    /// 查询类型
    pub chaxun_leixing: ditu_mingzi_chaxun_leixing,
    /// 查询值
    pub chaxun_zhi: String,
    /// 分页参数
    pub fenye_canshu: ditu_liebiao_chaxun_canshu,
}

impl ditu_mingzi_chaxun_tiaojian {
    /// 创建地图ID精确查询条件
    pub fn ditu_id_jingque(ditu_id: String, fenye_canshu: ditu_liebiao_chaxun_canshu) -> Self {
        Self {
            chaxun_leixing: ditu_mingzi_chaxun_leixing::ditu_id_jingque,
            chaxun_zhi: ditu_id,
            fenye_canshu,
        }
    }

    /// 创建名字精确查询条件
    pub fn mingzi_jingque(mingzi: String, fenye_canshu: ditu_liebiao_chaxun_canshu) -> Self {
        Self {
            chaxun_leixing: ditu_mingzi_chaxun_leixing::mingzi_jingque,
            chaxun_zhi: mingzi,
            fenye_canshu,
        }
    }

    /// 创建名字模糊查询条件
    pub fn mingzi_mohu(mingzi: String, fenye_canshu: ditu_liebiao_chaxun_canshu) -> Self {
        Self {
            chaxun_leixing: ditu_mingzi_chaxun_leixing::mingzi_mohu,
            chaxun_zhi: mingzi,
            fenye_canshu,
        }
    }
}

/// 地图名字搜索类型枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ditu_mingzi_sousuo_leixing {
    /// 精确匹配
    jingque,
    /// 模糊匹配
    mohu,
}

/// 地图名字搜索条件结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ditu_mingzi_sousuo_tiaojian {
    /// 搜索的名字
    pub mingzi: String,
    /// 搜索类型
    pub sousuo_leixing: ditu_mingzi_sousuo_leixing,
}

/// 地图联合搜索参数结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ditu_lianhe_sousuo_canshu {
    /// 地图分类（只能选择一个分类）
    pub ditu_fenlei: Option<String>,
    /// 名字搜索（可选）
    pub mingzi_sousuo: Option<ditu_mingzi_sousuo_tiaojian>,
    /// 分页参数
    pub fenye_canshu: ditu_liebiao_chaxun_canshu,
}

impl ditu_lianhe_sousuo_canshu {
    /// 生成搜索条件的哈希值（用于缓存键）
    pub fn shengcheng_tiaojian_hash(&self) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();

        // 将所有搜索条件加入哈希计算
        if let Some(ref fenlei) = self.ditu_fenlei {
            fenlei.hash(&mut hasher);
        }
        if let Some(ref mingzi) = self.mingzi_sousuo {
            mingzi.mingzi.hash(&mut hasher);
            format!("{:?}", mingzi.sousuo_leixing).hash(&mut hasher);
        }

        format!("{:x}", hasher.finish())
    }

    /// 验证搜索条件的有效性
    pub fn yanzheng_tiaojian_youxiao(&self) -> Result<(), String> {
        // 验证地图分类
        if let Some(ref fenlei) = self.ditu_fenlei {
            let zhichi_de_fenlei = vec![
                "leixing_town", "leixing_field", "leixing_dungeon", "leixing_quest",
                "leixing_instance", "leixing_siege", "leixing_pvp", "leixing_other", "leixing_weizhi",
                "fenlei_town", "fenlei_field", "fenlei_dungeon", "fenlei_quest",
                "fenlei_instance", "fenlei_siege", "fenlei_pvp", "fenlei_other", "fenlei_weizhi"
            ];

            if !zhichi_de_fenlei.contains(&fenlei.as_str()) {
                return Err(format!("无效的地图分类: {}", fenlei));
            }
        }

        // 验证名字搜索条件
        if let Some(ref mingzi) = self.mingzi_sousuo {
            if mingzi.mingzi.trim().is_empty() {
                return Err("名字搜索条件不能为空".to_string());
            }
        }

        Ok(())
    }

    /// 检查是否有任何搜索条件
    pub fn you_sousuo_tiaojian(&self) -> bool {
        self.ditu_fenlei.is_some() || self.mingzi_sousuo.is_some()
    }
}