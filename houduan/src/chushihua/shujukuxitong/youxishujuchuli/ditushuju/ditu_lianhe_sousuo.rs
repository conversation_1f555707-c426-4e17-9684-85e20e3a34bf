#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use super::ditu_redis_kongzhi::ditu_redis_kongzhi;
use super::ditu_rizhi_kongzhi::ditu_rizhi_kongzhi;
use super::ditu_sql_kongzhi::ditu_sql_kongzhi;
use super::ditushujujiegouti::{
    ditu_lianhe_sousuo_canshu, ditu_mingzi_sousuo_leixing, ditu_mingzi_sousuo_tiaojian,
    ditu_liebiao_jieguo, ditu_liebiao_xiangmu,
};
use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use crate::chushihua::shujukuxitong::redishujuku::redis_lianjie::redis_lianjie_guanli;
use serde_json;
use sqlx::Row;
use std::collections::HashMap;

/// 地图联合搜索管理器
pub struct ditu_lianhe_sousuo_guanli {
    mysql_lianjie: mysql_lianjie_guanli,
    redis_lianjie: Option<redis_lianjie_guanli>,
    redis_kongzhi: Option<ditu_redis_kongzhi>,
}

impl ditu_lianhe_sousuo_guanli {
    /// 创建不带Redis缓存的联合搜索管理器实例
    pub fn new(mysql_lianjie: mysql_lianjie_guanli) -> Self {
        Self {
            mysql_lianjie,
            redis_lianjie: None,
            redis_kongzhi: None,
        }
    }

    /// 创建带Redis缓存的联合搜索管理器实例
    pub fn new_with_redis(mysql_lianjie: mysql_lianjie_guanli, redis_lianjie: redis_lianjie_guanli) -> Self {
        let redis_kongzhi = ditu_redis_kongzhi::new(redis_lianjie.clone());
        Self {
            mysql_lianjie,
            redis_lianjie: Some(redis_lianjie),
            redis_kongzhi: Some(redis_kongzhi),
        }
    }

    /// 获取所有分类字段名
    fn huoqu_fenlei_ziduan() -> Vec<&'static str> {
        ditu_sql_kongzhi::huoqu_fenlei_ziduan()
    }

    /// 构建查询分类字段的SQL片段
    fn gouzao_fenlei_chaxun_sql_pianduan() -> String {
        ditu_sql_kongzhi::gouzao_fenlei_chaxun_sql_pianduan()
    }

    /// 联合搜索地图列表（主要入口方法）
    pub async fn lianhe_sousuo_ditu(&self, sousuo_canshu: &ditu_lianhe_sousuo_canshu) -> anyhow::Result<ditu_liebiao_jieguo> {
        // 验证搜索条件
        if let Err(cuowu) = sousuo_canshu.yanzheng_tiaojian_youxiao() {
            return Err(anyhow::anyhow!(cuowu));
        }

        // 检查是否有搜索条件
        if !sousuo_canshu.you_sousuo_tiaojian() {
            return Err(anyhow::anyhow!("联合搜索必须至少提供一个搜索条件"));
        }

        // 尝试从Redis获取缓存
        if let Some(redis_kongzhi) = &self.redis_kongzhi {
            let tiaojian_hash = sousuo_canshu.shengcheng_tiaojian_hash();
            if let Ok(Some(huancun_shuju)) = redis_kongzhi.huoqu_lianhe_sousuo_huancun(
                &tiaojian_hash,
                sousuo_canshu.fenye_canshu.mei_ye_shuliang,
                sousuo_canshu.fenye_canshu.dangqian_ye,
            ).await {
                if let Ok(mut jieguo) = serde_json::from_str::<ditu_liebiao_jieguo>(&huancun_shuju) {
                    // 设置数据来源为Redis
                    jieguo.shuju_laiyuan = Some("redis".to_string());
                    ditu_rizhi_kongzhi::huoqu_ditu_quanbu_xinxi_chenggong(&tiaojian_hash, ditu_rizhi_kongzhi::shuju_laiyuan_huancun);
                    return Ok(jieguo);
                }
            }
        }

        // 从数据库查询
        let jieguo = self.chaxun_shujuku_lianhe_sousuo(sousuo_canshu).await?;

        // 存储到Redis缓存
        if let Some(redis_kongzhi) = &self.redis_kongzhi {
            let tiaojian_hash = sousuo_canshu.shengcheng_tiaojian_hash();
            if let Ok(json_shuju) = serde_json::to_string(&jieguo) {
                let _ = redis_kongzhi.shezhi_lianhe_sousuo_huancun(
                    &tiaojian_hash,
                    sousuo_canshu.fenye_canshu.mei_ye_shuliang,
                    sousuo_canshu.fenye_canshu.dangqian_ye,
                    &json_shuju,
                ).await;
            }
        }

        let sousuo_info = format!("分类:{:?}, 名字:{:?}, 页码:{}:{}",
                                  sousuo_canshu.ditu_fenlei, sousuo_canshu.mingzi_sousuo,
                                  sousuo_canshu.fenye_canshu.dangqian_ye, sousuo_canshu.fenye_canshu.mei_ye_shuliang);
        ditu_rizhi_kongzhi::huoqu_ditu_quanbu_xinxi_chenggong(&sousuo_info, ditu_rizhi_kongzhi::shuju_laiyuan_shujuku);
        Ok(jieguo)
    }


    /// 从数据库查询联合搜索结果
    async fn chaxun_shujuku_lianhe_sousuo(&self, sousuo_canshu: &ditu_lianhe_sousuo_canshu) -> anyhow::Result<ditu_liebiao_jieguo> {
        // 先查询总数
        let zong_shuliang = self.chaxun_lianhe_sousuo_zongshu(sousuo_canshu).await?;

        // 构建查询SQL
        let (sql, canshu_liebiao) = self.shengcheng_lianhe_sousuo_sql(sousuo_canshu);
        let offset = sousuo_canshu.fenye_canshu.jisuan_offset();

        // 执行查询
        let mut query = sqlx::query(&sql);

        // 绑定参数
        for canshu in &canshu_liebiao {
            query = query.bind(canshu);
        }

        // 绑定分页参数
        query = query.bind(sousuo_canshu.fenye_canshu.mei_ye_shuliang)
            .bind(offset);

        let hanglie = query.fetch_all(self.mysql_lianjie.huoqu_lianjiechi()?).await?;
        let ditu_liebiao = self.jiexi_chaxun_jieguo(hanglie).await?;

        Ok(ditu_liebiao_jieguo::chenggong_with_laiyuan(
            ditu_liebiao,
            sousuo_canshu.fenye_canshu.dangqian_ye,
            zong_shuliang,
            sousuo_canshu.fenye_canshu.mei_ye_shuliang,
            "mysql".to_string(),
        ))
    }

    /// 查询联合搜索结果总数
    async fn chaxun_lianhe_sousuo_zongshu(&self, sousuo_canshu: &ditu_lianhe_sousuo_canshu) -> anyhow::Result<u32> {
        let (sql, canshu_liebiao) = self.shengcheng_lianhe_sousuo_zongshu_sql(sousuo_canshu);

        let mut query = sqlx::query(&sql);
        for canshu in &canshu_liebiao {
            query = query.bind(canshu);
        }

        let jieguo = query.fetch_one(self.mysql_lianjie.huoqu_lianjiechi()?).await?;
        let count: i64 = jieguo.get("count");
        Ok(count as u32)
    }

    /// 生成联合搜索SQL语句
    fn shengcheng_lianhe_sousuo_sql(&self, sousuo_canshu: &ditu_lianhe_sousuo_canshu) -> (String, Vec<String>) {
        let mut canshu_liebiao = Vec::new();

        // 确定搜索参数
        let mingzi_sousuo_jingque = if let Some(ref mingzi_sousuo) = sousuo_canshu.mingzi_sousuo {
            mingzi_sousuo.sousuo_leixing == ditu_mingzi_sousuo_leixing::jingque
        } else {
            false
        };

        let you_mingzi_sousuo = sousuo_canshu.mingzi_sousuo.is_some();

        // 使用SQL控制类生成SQL
        let sql = ditu_sql_kongzhi::shengcheng_lianhe_sousuo_sql(
            sousuo_canshu.ditu_fenlei.as_deref(),
            mingzi_sousuo_jingque,
            you_mingzi_sousuo,
        );

        // 添加名字搜索参数
        if let Some(ref mingzi_sousuo) = sousuo_canshu.mingzi_sousuo {
            match mingzi_sousuo.sousuo_leixing {
                ditu_mingzi_sousuo_leixing::jingque => {
                    canshu_liebiao.push(mingzi_sousuo.mingzi.clone());
                }
                ditu_mingzi_sousuo_leixing::mohu => {
                    canshu_liebiao.push(format!("%{}%", mingzi_sousuo.mingzi));
                }
            }
        }

        (sql, canshu_liebiao)
    }

    /// 生成联合搜索总数SQL语句
    fn shengcheng_lianhe_sousuo_zongshu_sql(&self, sousuo_canshu: &ditu_lianhe_sousuo_canshu) -> (String, Vec<String>) {
        let mut canshu_liebiao = Vec::new();

        // 确定搜索参数
        let mingzi_sousuo_jingque = if let Some(ref mingzi_sousuo) = sousuo_canshu.mingzi_sousuo {
            mingzi_sousuo.sousuo_leixing == ditu_mingzi_sousuo_leixing::jingque
        } else {
            false
        };

        let you_mingzi_sousuo = sousuo_canshu.mingzi_sousuo.is_some();

        // 使用SQL控制类生成SQL
        let sql = ditu_sql_kongzhi::shengcheng_lianhe_sousuo_zongshu_sql(
            sousuo_canshu.ditu_fenlei.as_deref(),
            mingzi_sousuo_jingque,
            you_mingzi_sousuo,
        );

        // 添加名字搜索参数
        if let Some(ref mingzi_sousuo) = sousuo_canshu.mingzi_sousuo {
            match mingzi_sousuo.sousuo_leixing {
                ditu_mingzi_sousuo_leixing::jingque => {
                    canshu_liebiao.push(mingzi_sousuo.mingzi.clone());
                }
                ditu_mingzi_sousuo_leixing::mohu => {
                    canshu_liebiao.push(format!("%{}%", mingzi_sousuo.mingzi));
                }
            }
        }

        (sql, canshu_liebiao)
    }

    /// 从name表查询地图名字
    async fn chaxun_ditu_mingzi_from_name(&self, ditu_id: &str) -> anyhow::Result<Option<String>> {
        let jieguo = sqlx::query("SELECT name FROM ditu_name WHERE id = ?")
            .bind(ditu_id)
            .fetch_optional(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;

        if let Some(hang) = jieguo {
            if let Ok(mingzi) = hang.try_get::<String, _>("name") {
                return Ok(Some(mingzi));
            }
        }
        Ok(None)
    }

    /// 处理分类字段，返回为true的字段
    fn chuli_fenlei_ziduan(&self, hang: &sqlx::mysql::MySqlRow) -> HashMap<String, bool> {
        let mut fenlei_map = HashMap::new();
        let fenlei_ziduan = Self::huoqu_fenlei_ziduan();

        for ziduan in fenlei_ziduan {
            if let Ok(zhi) = hang.try_get::<Option<String>, _>(ziduan) {
                if let Some(zhi_str) = zhi {
                    if zhi_str.to_lowercase() == "true" {
                        fenlei_map.insert(ziduan.to_string(), true);
                    }
                }
            }
        }

        fenlei_map
    }

    /// 解析查询结果行数据
    async fn jiexi_chaxun_jieguo(&self, rows: Vec<sqlx::mysql::MySqlRow>) -> anyhow::Result<Vec<ditu_liebiao_xiangmu>> {
        let mut ditu_liebiao = Vec::new();

        for hang in rows {
            let ditu_id: String = hang.get("ditu_id");
            let huizong_mingcheng: Option<String> = hang.try_get("ditu_mingcheng").unwrap_or(None);

            // 优先从name表获取名字，如果没有则使用huizong表的ditu_mingcheng
            let ditu_mingzi = match self.chaxun_ditu_mingzi_from_name(&ditu_id).await? {
                Some(name_mingzi) => name_mingzi,
                None => huizong_mingcheng.unwrap_or_else(|| "未知地图".to_string()),
            };

            // 处理分类字段
            let ditu_fenlei = self.chuli_fenlei_ziduan(&hang);

            let xiangmu = ditu_liebiao_xiangmu::new(ditu_id, ditu_mingzi, ditu_fenlei);
            ditu_liebiao.push(xiangmu);
        }

        Ok(ditu_liebiao)
    }

    /// 清除联合搜索缓存
    pub async fn qingchu_lianhe_sousuo_huancun(&self) -> anyhow::Result<u64> {
        if let Some(redis_kongzhi) = &self.redis_kongzhi {
            match redis_kongzhi.shanchu_suoyou_lianhe_sousuo_huancun().await {
                Ok(shuliang) => {
                    ditu_rizhi_kongzhi::redis_piliang_qingchu_chenggong(shuliang);
                    Ok(shuliang)
                }
                Err(e) => {
                    ditu_rizhi_kongzhi::redis_piliang_qingchu_shibai(&e.to_string());
                    Err(e)
                }
            }
        } else {
            Ok(0)
        }
    }
}
