#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use super::ditu_redis_kongzhi::ditu_redis_kongzhi;
use super::ditu_rizhi_kongzhi::ditu_rizhi_kongzhi;
use super::ditushujujiegouti::{ditu_fenlei_chaxun_canshu, ditu_liebiao_jieguo, ditu_liebiao_xiangmu};
use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use crate::chushihua::shujukuxitong::redishujuku::redis_lianjie::redis_lianjie_guanli;
use serde_json;
use sqlx::Row;
use std::collections::HashMap;

/// 地图分类查询类
pub struct ditu_fenlei_chaxun {
    mysql_lianjie: mysql_lianjie_guanli,
    redis_lianjie: Option<redis_lianjie_guanli>,
    redis_kongzhi: Option<ditu_redis_kongzhi>,
}

impl ditu_fenlei_chaxun {
    /// 创建新的地图分类查询实例
    pub fn new(mysql_lianjie: mysql_lianjie_guanli) -> Self {
        Self {
            mysql_lianjie,
            redis_lianjie: None,
            redis_kongzhi: None,
        }
    }

    /// 创建带Redis连接的地图分类查询实例
    pub fn new_with_redis(mysql_lianjie: mysql_lianjie_guanli, redis_lianjie: redis_lianjie_guanli) -> Self {
        let redis_kongzhi = ditu_redis_kongzhi::new(redis_lianjie.clone());
        Self {
            mysql_lianjie,
            redis_lianjie: Some(redis_lianjie),
            redis_kongzhi: Some(redis_kongzhi),
        }
    }

    /// 获取所有分类字段名
    fn huoqu_fenlei_ziduan() -> Vec<&'static str> {
        vec![
            "leixing_town", "leixing_field", "leixing_dungeon", "leixing_quest",
            "leixing_instance", "leixing_siege", "leixing_pvp", "leixing_other", "leixing_weizhi",
            "fenlei_town", "fenlei_field", "fenlei_dungeon", "fenlei_quest",
            "fenlei_instance", "fenlei_siege", "fenlei_pvp", "fenlei_other", "fenlei_weizhi"
        ]
    }

    /// 验证分类字段名是否有效
    fn yanzheng_fenlei_ziduan(ziduan_ming: &str) -> bool {
        Self::huoqu_fenlei_ziduan().contains(&ziduan_ming)
    }

    /// 构建分类查询的WHERE子句
    fn gouzao_where_ziju(&self, canshu: &ditu_fenlei_chaxun_canshu) -> (String, Vec<String>) {
        if !canshu.you_tiaojian() {
            return ("".to_string(), vec![]);
        }

        let mut where_tiaojian = Vec::new();
        let mut bind_zhi = Vec::new();

        for tiaojian in &canshu.fenlei_tiaojian {
            if Self::yanzheng_fenlei_ziduan(&tiaojian.ziduan_ming) {
                let zhi_str = if tiaojian.zhi { "true" } else { "false" };
                where_tiaojian.push(format!("{} = ?", tiaojian.ziduan_ming));
                bind_zhi.push(zhi_str.to_string());
            }
        }

        if where_tiaojian.is_empty() {
            return ("".to_string(), vec![]);
        }

        let luoji = if canshu.luoji_guanxi.to_uppercase() == "OR" { " OR " } else { " AND " };
        let where_ziju = format!("WHERE {}", where_tiaojian.join(luoji));

        (where_ziju, bind_zhi)
    }

    /// 构建分类查询SQL
    fn gouzao_fenlei_chaxun_sql(&self, canshu: &ditu_fenlei_chaxun_canshu) -> (String, Vec<String>) {
        let fenlei_ziduan = Self::huoqu_fenlei_ziduan();
        let ziduan_liebiaostr = fenlei_ziduan.join(", ");

        let (where_ziju, bind_zhi) = self.gouzao_where_ziju(canshu);

        let sql = if where_ziju.is_empty() {
            format!("SELECT ditu_id, ditu_mingcheng, {} FROM ditu_huizong LIMIT ? OFFSET ?", ziduan_liebiaostr)
        } else {
            format!("SELECT ditu_id, ditu_mingcheng, {} FROM ditu_huizong {} LIMIT ? OFFSET ?", ziduan_liebiaostr, where_ziju)
        };

        (sql, bind_zhi)
    }

    /// 构建分类查询总数SQL
    fn gouzao_fenlei_zongshu_sql(&self, canshu: &ditu_fenlei_chaxun_canshu) -> (String, Vec<String>) {
        let (where_ziju, bind_zhi) = self.gouzao_where_ziju(canshu);

        let sql = if where_ziju.is_empty() {
            "SELECT COUNT(*) as count FROM ditu_huizong".to_string()
        } else {
            format!("SELECT COUNT(*) as count FROM ditu_huizong {}", where_ziju)
        };

        (sql, bind_zhi)
    }

    /// 查询符合分类条件的地图总数量
    async fn chaxun_fenlei_ditu_zongshu(&self, canshu: &ditu_fenlei_chaxun_canshu) -> anyhow::Result<u32> {
        let lianjiechi = self.mysql_lianjie.huoqu_lianjiechi()?;
        let (sql, bind_zhi) = self.gouzao_fenlei_zongshu_sql(canshu);

        let mut query = sqlx::query(&sql);
        for zhi in bind_zhi {
            query = query.bind(zhi);
        }

        let jieguo = query.fetch_one(lianjiechi).await?;
        let zongshu: i64 = jieguo.get("count");
        Ok(zongshu as u32)
    }

    /// 从name表查询地图名字
    async fn chaxun_ditu_mingzi_from_name(&self, ditu_id: &str) -> anyhow::Result<Option<String>> {
        let lianjiechi = self.mysql_lianjie.huoqu_lianjiechi()?;

        let jieguo = sqlx::query("SELECT name FROM ditu_name WHERE id = ?")
            .bind(ditu_id)
            .fetch_optional(lianjiechi)
            .await?;

        if let Some(hang) = jieguo {
            if let Ok(mingzi) = hang.try_get::<String, _>("name") {
                return Ok(Some(mingzi));
            }
        }
        Ok(None)
    }

    /// 处理分类字段，返回为true的字段
    fn chuli_fenlei_ziduan(&self, hang: &sqlx::mysql::MySqlRow) -> HashMap<String, bool> {
        let mut fenlei_map = HashMap::new();
        let fenlei_ziduan = Self::huoqu_fenlei_ziduan();

        for ziduan in fenlei_ziduan {
            if let Ok(zhi) = hang.try_get::<Option<String>, _>(ziduan) {
                if let Some(zhi_str) = zhi {
                    if zhi_str.to_lowercase() == "true" {
                        fenlei_map.insert(ziduan.to_string(), true);
                    }
                }
            }
        }

        fenlei_map
    }

    /// 查询分类地图列表数据
    async fn chaxun_fenlei_ditu_liebiao_shuju(&self, canshu: &ditu_fenlei_chaxun_canshu) -> anyhow::Result<Vec<ditu_liebiao_xiangmu>> {
        let lianjiechi = self.mysql_lianjie.huoqu_lianjiechi()?;
        let (sql, bind_zhi) = self.gouzao_fenlei_chaxun_sql(canshu);
        let offset = canshu.jisuan_offset();

        let mut query = sqlx::query(&sql);
        for zhi in bind_zhi {
            query = query.bind(zhi);
        }
        query = query.bind(canshu.mei_ye_shuliang).bind(offset);

        let hanglie = query.fetch_all(lianjiechi).await?;
        let mut liebiao = Vec::new();

        for hang in hanglie {
            let ditu_id: String = hang.get("ditu_id");
            let huizong_mingcheng: Option<String> = hang.try_get("ditu_mingcheng").unwrap_or(None);

            // 优先从name表获取名字，如果没有则使用huizong表的ditu_mingcheng
            let ditu_mingzi = match self.chaxun_ditu_mingzi_from_name(&ditu_id).await? {
                Some(name_mingzi) => name_mingzi,
                None => huizong_mingcheng.unwrap_or_else(|| "未知地图".to_string()),
            };

            // 处理分类字段
            let ditu_fenlei = self.chuli_fenlei_ziduan(&hang);

            let xiangmu = ditu_liebiao_xiangmu::new(ditu_id, ditu_mingzi, ditu_fenlei);
            liebiao.push(xiangmu);
        }

        Ok(liebiao)
    }

    /// 生成Redis缓存键
    fn shengcheng_huancun_jian(&self, canshu: &ditu_fenlei_chaxun_canshu) -> String {
        let tiaojian_hash = canshu.shengcheng_tiaojian_hash();
        format!("ditu_fenlei:{}:{}:{}", tiaojian_hash, canshu.dangqian_ye, canshu.mei_ye_shuliang)
    }

    /// 查询分类地图列表（带缓存）
    pub async fn chaxun_fenlei_ditu_liebiao(&self, canshu: ditu_fenlei_chaxun_canshu) -> anyhow::Result<ditu_liebiao_jieguo> {
        // 先尝试从Redis获取缓存数据
        if let Some(redis_kongzhi) = &self.redis_kongzhi {
            let huancun_jian = self.shengcheng_huancun_jian(&canshu);
            if let Ok(Some(huancun_shuju)) = redis_kongzhi.huoqu_zidingyijian(&huancun_jian).await {
                if let Ok(mut jieguo) = serde_json::from_str::<ditu_liebiao_jieguo>(&huancun_shuju) {
                    // 设置数据来源为Redis
                    jieguo.shuju_laiyuan = Some("redis".to_string());
                    ditu_rizhi_kongzhi::huoqu_ditu_quanbu_xinxi_chenggong(&huancun_jian, ditu_rizhi_kongzhi::shuju_laiyuan_huancun);
                    return Ok(jieguo);
                }
            }
        }

        // 从数据库查询
        let zongshu = self.chaxun_fenlei_ditu_zongshu(&canshu).await?;
        let liebiao = self.chaxun_fenlei_ditu_liebiao_shuju(&canshu).await?;

        let jieguo = ditu_liebiao_jieguo::chenggong_with_laiyuan(
            liebiao,
            canshu.dangqian_ye,
            zongshu,
            canshu.mei_ye_shuliang,
            "mysql".to_string(),
        );

        // 缓存到Redis
        if let Some(redis_kongzhi) = &self.redis_kongzhi {
            let huancun_jian = self.shengcheng_huancun_jian(&canshu);
            if let Ok(json_shuju) = serde_json::to_string(&jieguo) {
                let _ = redis_kongzhi.shezhi_zidingyijian(&huancun_jian, &json_shuju, 3600).await; // 缓存1小时
            }
        }

        let tiaojian_info = format!("条件数量:{}, 逻辑:{}, 页码:{}:{}",
                                    canshu.fenlei_tiaojian.len(), canshu.luoji_guanxi, canshu.mei_ye_shuliang, canshu.dangqian_ye);
        ditu_rizhi_kongzhi::huoqu_ditu_quanbu_xinxi_chenggong(&tiaojian_info, ditu_rizhi_kongzhi::shuju_laiyuan_shujuku);
        Ok(jieguo)
    }

    /// 清除分类查询缓存
    pub async fn qingchu_fenlei_huancun(&self) -> anyhow::Result<u64> {
        if let Some(redis_kongzhi) = &self.redis_kongzhi {
            match redis_kongzhi.shanchu_pipei_jian("ditu_fenlei:*").await {
                Ok(shuliang) => {
                    ditu_rizhi_kongzhi::redis_piliang_qingchu_chenggong(shuliang);
                    Ok(shuliang)
                }
                Err(e) => {
                    ditu_rizhi_kongzhi::redis_piliang_qingchu_shibai(&e.to_string());
                    Err(e)
                }
            }
        } else {
            Ok(0)
        }
    }
}
