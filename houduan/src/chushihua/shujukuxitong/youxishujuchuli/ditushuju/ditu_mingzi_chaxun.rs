#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use super::ditu_redis_kongzhi::ditu_redis_kongzhi;
use super::ditu_rizhi_kongzhi::ditu_rizhi_kongzhi;
use super::ditushujujiegouti::{
    ditu_liebiao_chaxun_canshu, ditu_liebiao_jieguo,
    ditu_liebiao_xiang<PERSON>, ditu_mingzi_chaxun_leixing, ditu_mingzi_chaxun_tiaojian,
};
use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use crate::chushihua::shujukuxitong::redishujuku::redis_lianjie::redis_lianjie_guanli;
use serde_json;
use sqlx::Row;
use std::collections::HashMap;

/// 地图名字查询管理器
pub struct ditu_mingzi_chaxun_guanli {
    mysql_lianjie: mysql_lianjie_guanli,
    redis_lianjie: Option<redis_lianjie_guanli>,
    redis_kongzhi: Option<ditu_redis_kongzhi>,
}

impl ditu_mingzi_chaxun_guanli {
    /// 创建新的地图名字查询管理实例
    pub fn new(mysql_lianjie: mysql_lianjie_guanli) -> Self {
        Self {
            mysql_lianjie,
            redis_lianjie: None,
            redis_kongzhi: None,
        }
    }

    /// 创建带Redis连接的地图名字查询管理实例
    pub fn new_with_redis(mysql_lianjie: mysql_lianjie_guanli, redis_lianjie: redis_lianjie_guanli) -> Self {
        let redis_kongzhi = ditu_redis_kongzhi::new(redis_lianjie.clone());
        Self {
            mysql_lianjie,
            redis_lianjie: Some(redis_lianjie),
            redis_kongzhi: Some(redis_kongzhi),
        }
    }

    /// 获取所有分类字段名
    fn huoqu_fenlei_ziduan() -> Vec<&'static str> {
        vec![
            "leixing_town", "leixing_field", "leixing_dungeon", "leixing_quest",
            "leixing_instance", "leixing_siege", "leixing_pvp", "leixing_other", "leixing_weizhi",
            "fenlei_town", "fenlei_field", "fenlei_dungeon", "fenlei_quest",
            "fenlei_instance", "fenlei_siege", "fenlei_pvp", "fenlei_other", "fenlei_weizhi"
        ]
    }

    /// 构建查询分类字段的SQL片段
    fn gouzao_fenlei_chaxun_sql_pianduan() -> String {
        let fenlei_ziduan = Self::huoqu_fenlei_ziduan();
        fenlei_ziduan.join(", ")
    }

    /// 统一名字查询入口
    pub async fn tongyong_mingzi_chaxun(&self, chaxun_tiaojian: &ditu_mingzi_chaxun_tiaojian) -> anyhow::Result<ditu_liebiao_jieguo> {
        // 尝试从Redis获取缓存
        if let Some(redis_kongzhi) = &self.redis_kongzhi {
            let huancun_jian = self.shengcheng_huancun_jian(chaxun_tiaojian);
            if let Ok(Some(huancun_shuju)) = redis_kongzhi.huoqu_zidingyijian(&huancun_jian).await {
                if let Ok(mut jieguo) = serde_json::from_str::<ditu_liebiao_jieguo>(&huancun_shuju) {
                    // 设置数据来源为Redis
                    jieguo.shuju_laiyuan = Some("redis".to_string());
                    ditu_rizhi_kongzhi::huoqu_ditu_quanbu_xinxi_chenggong(&huancun_jian, ditu_rizhi_kongzhi::shuju_laiyuan_huancun);
                    return Ok(jieguo);
                }
            }
        }

        // 从数据库查询
        let jieguo = match chaxun_tiaojian.chaxun_leixing {
            ditu_mingzi_chaxun_leixing::ditu_id_jingque => {
                self.chaxun_by_ditu_id_jingque(&chaxun_tiaojian.chaxun_zhi, &chaxun_tiaojian.fenye_canshu).await
            }
            ditu_mingzi_chaxun_leixing::mingzi_jingque => {
                self.chaxun_by_mingzi_jingque(&chaxun_tiaojian.chaxun_zhi, &chaxun_tiaojian.fenye_canshu).await
            }
            ditu_mingzi_chaxun_leixing::mingzi_mohu => {
                self.chaxun_by_mingzi_mohu(&chaxun_tiaojian.chaxun_zhi, &chaxun_tiaojian.fenye_canshu).await
            }
        };

        match jieguo {
            Ok(liebiao_jieguo) => {
                // 缓存到Redis
                if let Some(redis_kongzhi) = &self.redis_kongzhi {
                    let huancun_jian = self.shengcheng_huancun_jian(chaxun_tiaojian);
                    if let Ok(json_shuju) = serde_json::to_string(&liebiao_jieguo) {
                        let _ = redis_kongzhi.shezhi_zidingyijian(&huancun_jian, &json_shuju, 3600).await; // 缓存1小时
                    }
                }

                let chaxun_info = format!("类型:{}, 值:{}, 页码:{}:{}",
                                          chaxun_tiaojian.chaxun_leixing.to_string(), chaxun_tiaojian.chaxun_zhi,
                                          chaxun_tiaojian.fenye_canshu.dangqian_ye, chaxun_tiaojian.fenye_canshu.mei_ye_shuliang);
                ditu_rizhi_kongzhi::huoqu_ditu_quanbu_xinxi_chenggong(&chaxun_info, ditu_rizhi_kongzhi::shuju_laiyuan_shujuku);
                Ok(liebiao_jieguo)
            }
            Err(e) => {
                ditu_rizhi_kongzhi::shujuku_chaxun_shibai("地图名字查询", &e.to_string());
                Err(e)
            }
        }
    }

    /// 生成Redis缓存键
    fn shengcheng_huancun_jian(&self, chaxun_tiaojian: &ditu_mingzi_chaxun_tiaojian) -> String {
        format!("ditu_mingzi:{}:{}:{}:{}",
                chaxun_tiaojian.chaxun_leixing.to_string(),
                chaxun_tiaojian.chaxun_zhi,
                chaxun_tiaojian.fenye_canshu.dangqian_ye,
                chaxun_tiaojian.fenye_canshu.mei_ye_shuliang)
    }

    /// 根据地图ID精确查询地图列表
    pub async fn chaxun_by_ditu_id_jingque(&self, ditu_id: &str, fenye_canshu: &ditu_liebiao_chaxun_canshu) -> anyhow::Result<ditu_liebiao_jieguo> {
        let fenlei_ziduan = Self::gouzao_fenlei_chaxun_sql_pianduan();

        // 查询总数
        let zongshu_sql = "SELECT COUNT(*) as count FROM ditu_huizong WHERE ditu_id = ?";
        let zongshu = self.zhixing_zongshu_chaxun(ditu_id, zongshu_sql).await?;

        // 查询分页数据
        let fenye_sql = format!("SELECT ditu_id, ditu_mingcheng, {} FROM ditu_huizong WHERE ditu_id = ? LIMIT ? OFFSET ?", fenlei_ziduan);
        let offset = fenye_canshu.jisuan_offset();

        let hanglie = sqlx::query(&fenye_sql)
            .bind(ditu_id)
            .bind(fenye_canshu.mei_ye_shuliang)
            .bind(offset)
            .fetch_all(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;

        let ditu_liebiao = self.jiexi_chaxun_jieguo(hanglie).await?;
        Ok(ditu_liebiao_jieguo::chenggong_with_laiyuan(ditu_liebiao, fenye_canshu.dangqian_ye, zongshu, fenye_canshu.mei_ye_shuliang, "mysql".to_string()))
    }

    /// 根据名字精确查询地图列表
    pub async fn chaxun_by_mingzi_jingque(&self, mingzi: &str, fenye_canshu: &ditu_liebiao_chaxun_canshu) -> anyhow::Result<ditu_liebiao_jieguo> {
        let fenlei_ziduan = Self::gouzao_fenlei_chaxun_sql_pianduan();

        // 查询总数 - 优先从name表查询，然后从huizong表查询
        let zongshu_sql = r#"
            SELECT COUNT(DISTINCT h.ditu_id) as count 
            FROM ditu_huizong h 
            LEFT JOIN ditu_name n ON h.ditu_id = n.id 
            WHERE COALESCE(n.name, h.ditu_mingcheng) = ?
        "#;
        let zongshu = self.zhixing_zongshu_chaxun(mingzi, zongshu_sql).await?;

        // 查询分页数据
        let fenye_sql = format!(r#"
            SELECT h.ditu_id, h.ditu_mingcheng, {} 
            FROM ditu_huizong h 
            LEFT JOIN ditu_name n ON h.ditu_id = n.id 
            WHERE COALESCE(n.name, h.ditu_mingcheng) = ? 
            LIMIT ? OFFSET ?
        "#, fenlei_ziduan);
        let offset = fenye_canshu.jisuan_offset();

        let hanglie = sqlx::query(&fenye_sql)
            .bind(mingzi)
            .bind(fenye_canshu.mei_ye_shuliang)
            .bind(offset)
            .fetch_all(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;

        let ditu_liebiao = self.jiexi_chaxun_jieguo(hanglie).await?;
        Ok(ditu_liebiao_jieguo::chenggong_with_laiyuan(ditu_liebiao, fenye_canshu.dangqian_ye, zongshu, fenye_canshu.mei_ye_shuliang, "mysql".to_string()))
    }

    /// 根据名字模糊查询地图列表
    pub async fn chaxun_by_mingzi_mohu(&self, mingzi: &str, fenye_canshu: &ditu_liebiao_chaxun_canshu) -> anyhow::Result<ditu_liebiao_jieguo> {
        let fenlei_ziduan = Self::gouzao_fenlei_chaxun_sql_pianduan();
        let mohu_moshi = format!("%{}%", mingzi);

        // 查询总数 - 优先从name表查询，然后从huizong表查询
        let zongshu_sql = r#"
            SELECT COUNT(DISTINCT h.ditu_id) as count 
            FROM ditu_huizong h 
            LEFT JOIN ditu_name n ON h.ditu_id = n.id 
            WHERE COALESCE(n.name, h.ditu_mingcheng) LIKE ?
        "#;
        let zongshu = self.zhixing_zongshu_chaxun(&mohu_moshi, zongshu_sql).await?;

        // 查询分页数据
        let fenye_sql = format!(r#"
            SELECT h.ditu_id, h.ditu_mingcheng, {} 
            FROM ditu_huizong h 
            LEFT JOIN ditu_name n ON h.ditu_id = n.id 
            WHERE COALESCE(n.name, h.ditu_mingcheng) LIKE ? 
            LIMIT ? OFFSET ?
        "#, fenlei_ziduan);
        let offset = fenye_canshu.jisuan_offset();

        let hanglie = sqlx::query(&fenye_sql)
            .bind(&mohu_moshi)
            .bind(fenye_canshu.mei_ye_shuliang)
            .bind(offset)
            .fetch_all(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;

        let ditu_liebiao = self.jiexi_chaxun_jieguo(hanglie).await?;
        Ok(ditu_liebiao_jieguo::chenggong_with_laiyuan(ditu_liebiao, fenye_canshu.dangqian_ye, zongshu, fenye_canshu.mei_ye_shuliang, "mysql".to_string()))
    }

    /// 通用总数查询执行方法
    async fn zhixing_zongshu_chaxun(&self, chaxun_zhi: &str, sql: &str) -> anyhow::Result<u32> {
        let jieguo = sqlx::query(sql)
            .bind(chaxun_zhi)
            .fetch_one(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;

        let zongshu: i64 = jieguo.get("count");
        Ok(zongshu as u32)
    }

    /// 从name表查询地图名字
    async fn chaxun_ditu_mingzi_from_name(&self, ditu_id: &str) -> anyhow::Result<Option<String>> {
        let jieguo = sqlx::query("SELECT name FROM ditu_name WHERE id = ?")
            .bind(ditu_id)
            .fetch_optional(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;

        if let Some(hang) = jieguo {
            if let Ok(mingzi) = hang.try_get::<String, _>("name") {
                return Ok(Some(mingzi));
            }
        }
        Ok(None)
    }

    /// 处理分类字段，返回为true的字段
    fn chuli_fenlei_ziduan(&self, hang: &sqlx::mysql::MySqlRow) -> HashMap<String, bool> {
        let mut fenlei_map = HashMap::new();
        let fenlei_ziduan = Self::huoqu_fenlei_ziduan();

        for ziduan in fenlei_ziduan {
            if let Ok(zhi) = hang.try_get::<Option<String>, _>(ziduan) {
                if let Some(zhi_str) = zhi {
                    if zhi_str.to_lowercase() == "true" {
                        fenlei_map.insert(ziduan.to_string(), true);
                    }
                }
            }
        }

        fenlei_map
    }

    /// 解析查询结果行数据
    async fn jiexi_chaxun_jieguo(&self, rows: Vec<sqlx::mysql::MySqlRow>) -> anyhow::Result<Vec<ditu_liebiao_xiangmu>> {
        let mut ditu_liebiao = Vec::new();

        for hang in rows {
            let ditu_id: String = hang.get("ditu_id");
            let huizong_mingcheng: Option<String> = hang.try_get("ditu_mingcheng").unwrap_or(None);

            // 优先从name表获取名字，如果没有则使用huizong表的ditu_mingcheng
            let ditu_mingzi = match self.chaxun_ditu_mingzi_from_name(&ditu_id).await? {
                Some(name_mingzi) => name_mingzi,
                None => huizong_mingcheng.unwrap_or_else(|| "未知地图".to_string()),
            };

            // 处理分类字段
            let ditu_fenlei = self.chuli_fenlei_ziduan(&hang);

            let xiangmu = ditu_liebiao_xiangmu::new(ditu_id, ditu_mingzi, ditu_fenlei);
            ditu_liebiao.push(xiangmu);
        }

        Ok(ditu_liebiao)
    }

    /// 清除名字查询缓存
    pub async fn qingchu_mingzi_chaxun_huancun(&self) -> anyhow::Result<u64> {
        if let Some(redis_kongzhi) = &self.redis_kongzhi {
            match redis_kongzhi.shanchu_pipei_jian("ditu_mingzi:*").await {
                Ok(shuliang) => {
                    ditu_rizhi_kongzhi::redis_piliang_qingchu_chenggong(shuliang);
                    Ok(shuliang)
                }
                Err(e) => {
                    ditu_rizhi_kongzhi::redis_piliang_qingchu_shibai(&e.to_string());
                    Err(e)
                }
            }
        } else {
            Ok(0)
        }
    }
}
