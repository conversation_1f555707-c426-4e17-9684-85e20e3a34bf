#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use super::wangzhanjichuxinxi_peizhi::wangzhanjichuxinxi_peizhi;
use crate::rizhixitong::{rizhixitong_cuowu_with_moshi, rizhixitong_shuchu_moshi, rizhixitong_xinxi_with_moshi};
use anyhow::Result;
use std::fs;
use std::sync::{Arc, RwLock};

/// 网站基础信息配置管理器
///
/// 负责网站基础信息配置的创建、加载、热更新等所有管理逻辑
/// 通过调用配置结构体来实现具体功能，保证扩展性
pub struct wangzhanjichuxinxi_guanli {
    peizhi: Arc<RwLock<wangzhanjichuxinxi_peizhi>>,
}

impl wangzhanjichuxinxi_guanli {
    /// 创建新的网站基础信息配置管理器
    pub fn wangzhanjichuxinxi_new() -> Self {
        Self {
            peizhi: Arc::new(RwLock::new(wangzhanjichuxinxi_peizhi::default())),
        }
    }

    /// 初始化网站基础信息配置系统
    ///
    /// 尝试读取配置文件，如果失败则创建默认配置文件
    pub fn wangzhanjichuxinxi_chushihua(&self) -> Result<()> {
        // 尝试从文件读取配置
        match self.wangzhanjichuxinxi_cong_wenjian_duqu() {
            Ok(peizhi) => {
                // 成功读取，更新内存中的配置
                match self.peizhi.write() {
                    Ok(mut guard) => {
                        *guard = peizhi;
                        rizhixitong_xinxi_with_moshi(
                            "网站基础信息配置系统初始化成功啦！",
                            rizhixitong_shuchu_moshi::xiangxi,
                        );
                        Ok(())
                    }
                    Err(e) => {
                        rizhixitong_cuowu_with_moshi(
                            &format!("更新网站基础信息内存配置失败: {}", e),
                            rizhixitong_shuchu_moshi::xiangxi,
                        );
                        Err(anyhow::anyhow!("更新网站基础信息内存配置失败: {}", e))
                    }
                }
            }
            Err(_) => {
                // 读取失败，创建默认配置文件
                self.wangzhanjichuxinxi_chuangjian_moren_wenjian()?;

                rizhixitong_xinxi_with_moshi(
                    "网站基础信息配置系统初始化成功啦！",
                    rizhixitong_shuchu_moshi::xiangxi,
                );
                Ok(())
            }
        }
    }

    /// 从文件读取网站基础信息配置
    fn wangzhanjichuxinxi_cong_wenjian_duqu(&self) -> Result<wangzhanjichuxinxi_peizhi> {
        let wenjian_lujing = wangzhanjichuxinxi_peizhi::wangzhanjichuxinxi_get_wenjian_lujing();

        match fs::read_to_string(&wenjian_lujing) {
            Ok(neirong) => {
                match serde_yaml::from_str::<wangzhanjichuxinxi_peizhi>(&neirong) {
                    Ok(peizhi) => {
                        Ok(peizhi)
                    }
                    Err(e) => {
                        Err(anyhow::anyhow!("网站基础信息配置文件解析失败: {}", e))
                    }
                }
            }
            Err(e) => {
                Err(anyhow::anyhow!("读取网站基础信息配置文件失败: {}", e))
            }
        }
    }

    /// 创建默认网站基础信息配置文件
    fn wangzhanjichuxinxi_chuangjian_moren_wenjian(&self) -> Result<()> {
        let wenjian_lujing = wangzhanjichuxinxi_peizhi::wangzhanjichuxinxi_get_wenjian_lujing();
        let moren_peizhi = wangzhanjichuxinxi_peizhi::default();

        rizhixitong_xinxi_with_moshi(
            &format!("创建默认网站基础信息配置文件: {}", wenjian_lujing),
            rizhixitong_shuchu_moshi::xiangxi,
        );

        self.wangzhanjichuxinxi_baocun_peizhi_dao_wenjian(&moren_peizhi)
    }

    /// 保存网站基础信息配置到文件
    fn wangzhanjichuxinxi_baocun_peizhi_dao_wenjian(&self, peizhi: &wangzhanjichuxinxi_peizhi) -> Result<()> {
        let wenjian_lujing = wangzhanjichuxinxi_peizhi::wangzhanjichuxinxi_get_wenjian_lujing();

        match serde_yaml::to_string(peizhi) {
            Ok(yaml_neirong) => {
                match fs::write(&wenjian_lujing, yaml_neirong) {
                    Ok(_) => {
                        rizhixitong_xinxi_with_moshi(
                            "网站基础信息配置文件保存成功",
                            rizhixitong_shuchu_moshi::xiangxi,
                        );
                        Ok(())
                    }
                    Err(e) => {
                        rizhixitong_cuowu_with_moshi(
                            &format!("写入网站基础信息配置文件失败: {}", e),
                            rizhixitong_shuchu_moshi::xiangxi,
                        );
                        Err(anyhow::anyhow!("写入网站基础信息配置文件失败: {}", e))
                    }
                }
            }
            Err(e) => {
                rizhixitong_cuowu_with_moshi(
                    &format!("序列化网站基础信息配置失败: {}", e),
                    rizhixitong_shuchu_moshi::xiangxi,
                );
                Err(anyhow::anyhow!("序列化网站基础信息配置失败: {}", e))
            }
        }
    }

    /// 热加载网站基础信息配置文件
    pub fn wangzhanjichuxinxi_re_jiazai(&self) -> Result<()> {
        rizhixitong_xinxi_with_moshi(
            "开始热加载网站基础信息配置文件...",
            rizhixitong_shuchu_moshi::xiangxi,
        );

        match self.wangzhanjichuxinxi_cong_wenjian_duqu() {
            Ok(xin_peizhi) => {
                match self.peizhi.write() {
                    Ok(mut guard) => {
                        *guard = xin_peizhi;
                        rizhixitong_xinxi_with_moshi(
                            "网站基础信息配置热加载成功",
                            rizhixitong_shuchu_moshi::xiangxi,
                        );
                        Ok(())
                    }
                    Err(e) => {
                        rizhixitong_cuowu_with_moshi(
                            &format!("更新网站基础信息内存配置失败: {}", e),
                            rizhixitong_shuchu_moshi::xiangxi,
                        );
                        Err(anyhow::anyhow!("更新网站基础信息内存配置失败: {}", e))
                    }
                }
            }
            Err(e) => {
                rizhixitong_cuowu_with_moshi(
                    &format!("热加载网站基础信息配置失败: {}", e),
                    rizhixitong_shuchu_moshi::xiangxi,
                );
                Err(e)
            }
        }
    }

    /// 获取当前网站基础信息配置的克隆
    pub fn wangzhanjichuxinxi_get_peizhi(&self) -> Result<wangzhanjichuxinxi_peizhi> {
        match self.peizhi.read() {
            Ok(guard) => Ok(guard.clone()),
            Err(e) => {
                rizhixitong_cuowu_with_moshi(
                    &format!("读取网站基础信息配置失败: {}", e),
                    rizhixitong_shuchu_moshi::xiangxi,
                );
                Err(anyhow::anyhow!("读取网站基础信息配置失败: {}", e))
            }
        }
    }

    /// 更新网站基础信息配置并保存到文件
    pub fn wangzhanjichuxinxi_gengxin_peizhi(&self, xin_peizhi: wangzhanjichuxinxi_peizhi) -> Result<()> {
        rizhixitong_xinxi_with_moshi(
            "更新网站基础信息配置...",
            rizhixitong_shuchu_moshi::xiangxi,
        );

        // 先保存到文件
        self.wangzhanjichuxinxi_baocun_peizhi_dao_wenjian(&xin_peizhi)?;

        // 再更新内存
        match self.peizhi.write() {
            Ok(mut guard) => {
                *guard = xin_peizhi;
                rizhixitong_xinxi_with_moshi(
                    "网站基础信息配置更新成功",
                    rizhixitong_shuchu_moshi::xiangxi,
                );
                Ok(())
            }
            Err(e) => {
                rizhixitong_cuowu_with_moshi(
                    &format!("更新网站基础信息内存配置失败: {}", e),
                    rizhixitong_shuchu_moshi::xiangxi,
                );
                Err(anyhow::anyhow!("更新网站基础信息内存配置失败: {}", e))
            }
        }
    }
}
