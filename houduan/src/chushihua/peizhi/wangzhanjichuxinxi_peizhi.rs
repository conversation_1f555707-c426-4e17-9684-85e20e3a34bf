#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use serde::{Deserialize, Serialize};
use std::path::Path;

/// 网站导航项配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct wangzhan_daohang_xiangmu {
    pub mingcheng: String,    // 导航项名称
    pub lianjie: String,      // 导航项链接
}

impl Default for wangzhan_daohang_xiangmu {
    fn default() -> Self {
        Self {
            mingcheng: "默认导航".to_string(),
            lianjie: "/".to_string(),
        }
    }
}

/// 网站SEO信息配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct wangzhan_seo_xinxi {
    pub wangzhan_miaoshu: String,        // 网站描述
    pub guanjianci: Vec<String>,         // 关键词列表
    pub zuozhe: String,                  // 作者信息
    pub banquan_xinxi: String,           // 版权信息
}

impl Default for wangzhan_seo_xinxi {
    fn default() -> Self {
        Self {
            wangzhan_miaoshu: "RO百科 - 专业的仙境传说资料站，提供全面的游戏数据查询服务".to_string(),
            guanjianci: vec![
                "RO百科".to_string(),
                "仙境传说".to_string(),
                "游戏资料".to_string(),
                "怪物数据".to_string(),
                "物品数据".to_string(),
                "地图数据".to_string(),
                "技能数据".to_string(),
            ],
            zuozhe: "RO百科团队".to_string(),
            banquan_xinxi: "© 2024 RO百科. All rights reserved.".to_string(),
        }
    }
}

/// 网站基础信息总配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct wangzhanjichuxinxi_peizhi {
    pub wangzhan_mingcheng: String,                    // 网站名称
    pub wangzhan_tubiao_lianjie: String,              // 网站图标链接
    pub dingbu_daohang: Vec<wangzhan_daohang_xiangmu>, // 顶部导航列表
    pub seo_xinxi: wangzhan_seo_xinxi,                // SEO信息
}

impl Default for wangzhanjichuxinxi_peizhi {
    fn default() -> Self {
        Self {
            wangzhan_mingcheng: "RO百科".to_string(),
            wangzhan_tubiao_lianjie: "https://i0.hdslb.com/bfs/face/e9ced5e9b9c2621b58bc1a36670975e181429d1e.jpg@240w_240h_1c_1s_!web-avatar-nav.avif".to_string(),
            dingbu_daohang: vec![
                wangzhan_daohang_xiangmu {
                    mingcheng: "怪物数据".to_string(),
                    lianjie: "/guaiwushuju".to_string(),
                },
                wangzhan_daohang_xiangmu {
                    mingcheng: "物品数据".to_string(),
                    lianjie: "/wupinshuju".to_string(),
                },
                wangzhan_daohang_xiangmu {
                    mingcheng: "地图数据".to_string(),
                    lianjie: "/ditushuju".to_string(),
                },
                wangzhan_daohang_xiangmu {
                    mingcheng: "技能数据".to_string(),
                    lianjie: "/jinengshuju".to_string(),
                },
            ],
            seo_xinxi: wangzhan_seo_xinxi::default(),
        }
    }
}

impl wangzhanjichuxinxi_peizhi {
    /// 获取网站基础信息配置文件路径
    pub fn wangzhanjichuxinxi_get_wenjian_lujing() -> String {
        "./peizhi/wangzhanjichuxinxi.yml".to_string()
    }

    /// 检查配置文件是否存在
    pub fn wangzhanjichuxinxi_wenjian_cunzai() -> bool {
        let lujing = Self::wangzhanjichuxinxi_get_wenjian_lujing();
        Path::new(&lujing).exists()
    }
}
