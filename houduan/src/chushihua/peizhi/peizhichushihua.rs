#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use crate::rizhixitong::{rizhixitong_cuowu_with_moshi, rizhixitong_jinggao_with_moshi, rizhixitong_shuchu_moshi, rizhixitong_xinxi_with_moshi};
use std::path::Path;

/// 配置系统初始化器
pub struct peizhixitong_chushihua;

impl peizhixitong_chushihua {
    /// 检测项目根目录是否存在配置文件夹和配置文件
    ///
    /// 检查以下路径：
    /// - ./peizhi/ 文件夹是否存在
    /// - ./peizhi/zongpeizhi.yml 文件是否存在
    ///
    /// 返回：
    /// - true: 配置文件夹和配置文件都存在
    /// - false: 配置文件夹或配置文件不存在
    pub fn peizhixitong_jiance_peizhi_wenjian() -> bool {
        rizhixitong_xinxi_with_moshi(
            "开始检测配置文件...",
            rizhixitong_shuchu_moshi::xiangxi
        );

        let peizhi_mulu = Path::new("./peizhi");
        let zongpeizhi_wenjian = peizhi_mulu.join("zongpeizhi.yml");

        // 检查配置文件夹是否存在
        if !peizhi_mulu.exists() {
            rizhixitong_jinggao_with_moshi(
                "配置文件夹 ./peizhi 不存在",
                rizhixitong_shuchu_moshi::xiangxi
            );
            return false;
        }

        if !peizhi_mulu.is_dir() {
            rizhixitong_cuowu_with_moshi(
                "./peizhi 存在但不是文件夹",
                rizhixitong_shuchu_moshi::xiangxi
            );
            return false;
        }

        // 检查配置文件是否存在
        if !zongpeizhi_wenjian.exists() {
            rizhixitong_jinggao_with_moshi(
                "配置文件 ./peizhi/zongpeizhi.yml 不存在",
                rizhixitong_shuchu_moshi::xiangxi
            );
            return false;
        }

        if !zongpeizhi_wenjian.is_file() {
            rizhixitong_cuowu_with_moshi(
                "./peizhi/zongpeizhi.yml 存在但不是文件",
                rizhixitong_shuchu_moshi::xiangxi
            );
            return false;
        }

        rizhixitong_xinxi_with_moshi(
            "配置文件检测通过：./peizhi/zongpeizhi.yml 存在",
            rizhixitong_shuchu_moshi::xiangxi
        );

        true
    }

    /// 检测网站基础信息配置文件
    ///
    /// 检查以下路径：
    /// - ./peizhi/wangzhanjichuxinxi.yml 文件是否存在
    ///
    /// 返回：
    /// - true: 网站基础信息配置文件存在
    /// - false: 网站基础信息配置文件不存在
    pub fn wangzhanjichuxinxi_jiance_peizhi_wenjian() -> bool {
        rizhixitong_xinxi_with_moshi(
            "开始检测网站基础信息配置文件...",
            rizhixitong_shuchu_moshi::xiangxi,
        );

        let peizhi_mulu = Path::new("./peizhi");
        let wangzhanjichuxinxi_wenjian = peizhi_mulu.join("wangzhanjichuxinxi.yml");

        // 检查配置文件夹是否存在
        if !peizhi_mulu.exists() {
            rizhixitong_jinggao_with_moshi(
                "配置文件夹 ./peizhi 不存在",
                rizhixitong_shuchu_moshi::xiangxi,
            );
            return false;
        }

        // 检查网站基础信息配置文件是否存在
        if !wangzhanjichuxinxi_wenjian.exists() {
            rizhixitong_jinggao_with_moshi(
                "网站基础信息配置文件 ./peizhi/wangzhanjichuxinxi.yml 不存在",
                rizhixitong_shuchu_moshi::xiangxi,
            );
            return false;
        }

        if !wangzhanjichuxinxi_wenjian.is_file() {
            rizhixitong_cuowu_with_moshi(
                "./peizhi/wangzhanjichuxinxi.yml 存在但不是文件",
                rizhixitong_shuchu_moshi::xiangxi,
            );
            return false;
        }

        rizhixitong_xinxi_with_moshi(
            "网站基础信息配置文件检测通过：./peizhi/wangzhanjichuxinxi.yml 存在",
            rizhixitong_shuchu_moshi::xiangxi,
        );

        true
    }
    
    /// 获取配置文件的完整路径
    pub fn peizhixitong_get_peizhi_lujing() -> String {
        "./peizhi/zongpeizhi.yml".to_string()
    }

    /// 获取网站基础信息配置文件的完整路径
    pub fn wangzhanjichuxinxi_get_peizhi_lujing() -> String {
        "./peizhi/wangzhanjichuxinxi.yml".to_string()
    }

    /// 获取配置文件夹的完整路径
    pub fn peizhixitong_get_peizhi_mulu() -> String {
        "./peizhi".to_string()
    }
    
    /// 创建配置文件夹（如果不存在）
    pub fn peizhixitong_chuangjian_peizhi_mulu() -> Result<(), std::io::Error> {
        let peizhi_mulu = Path::new("./peizhi");
        
        if !peizhi_mulu.exists() {
            rizhixitong_xinxi_with_moshi(
                "创建配置文件夹 ./peizhi", 
                rizhixitong_shuchu_moshi::xiangxi
            );
            
            std::fs::create_dir_all(peizhi_mulu)?;
            
            rizhixitong_xinxi_with_moshi(
                "配置文件夹创建成功", 
                rizhixitong_shuchu_moshi::xiangxi
            );
        } else {
            rizhixitong_xinxi_with_moshi(
                "配置文件夹已存在", 
                rizhixitong_shuchu_moshi::jinmo
            );
        }
        
        Ok(())
    }
}
