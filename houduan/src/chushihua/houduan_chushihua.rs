#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use crate::chushihua::peizhi::{peizhixitong_chushihua, peizhixitong_guanli::peizhixitong_guanli, wangzhanjichuxinxi_guanli::wangzhanjichuxinxi_guanli};
use crate::chushihua::shujukuxitong::mysqlshujuku::{mysql_chushihua, mysql_lianjie::mysql_lianjie_guanli};
use crate::chushihua::shujukuxitong::redishujuku::{redis_chushihua, redis_lianjie_guanli};
use crate::rizhixitong::rizhixitong_chushihua_rizhixitong;
use anyhow::Result;
use rand::Rng;
use std::net::TcpListener;

use rocket::Config;

/// 后端系统初始化器
pub struct houduan_chushihua;

impl houduan_chushihua {
    /// 初始化整个后端系统
    ///
    /// 按以下顺序初始化：
    /// 1. 日志系统
    /// 2. 配置系统
    /// 3. 网站基础信息配置系统
    /// 4. mysql数据库
    /// 5. redis数据库
    ///
    /// 返回配置管理器和数据库连接管理器实例，供后续使用
    pub async fn houduan_chushihua_xitong() -> Result<(peizhixitong_guanli, wangzhanjichuxinxi_guanli, mysql_lianjie_guanli, redis_lianjie_guanli)> {
        // 1. 首先初始化日志系统
        rizhixitong_chushihua_rizhixitong().await?;

        // 显示日志系统初始化成功消息
        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
            "日志系统初始化成功啦！",
            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
        );



        // 2. 创建配置文件夹（如果不存在）
        if let Err(e) = peizhixitong_chushihua::peizhixitong_chuangjian_peizhi_mulu() {
            crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                &format!("创建配置文件夹失败: {}", e),
                crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
            );
            return Err(anyhow::anyhow!("创建配置文件夹失败: {}", e));
        }

        // 3. 初始化配置系统
        let peizhi_guanliqii = peizhixitong_guanli::peizhixitong_new();

        if let Err(e) = peizhi_guanliqii.peizhixitong_chushihua() {
            crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                &format!("配置系统初始化失败: {}", e),
                crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
            );
            return Err(anyhow::anyhow!("配置系统初始化失败: {}", e));
        }

        // 4. 初始化网站基础信息配置系统
        let wangzhanjichuxinxi_guanliqii = wangzhanjichuxinxi_guanli::wangzhanjichuxinxi_new();

        if let Err(e) = wangzhanjichuxinxi_guanliqii.wangzhanjichuxinxi_chushihua() {
            crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                &format!("网站基础信息配置系统初始化失败: {}", e),
                crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
            );
            return Err(anyhow::anyhow!("网站基础信息配置系统初始化失败: {}", e));
        }


        // 5. 初始化mysql数据库
        let mysql_guanliqi = match mysql_chushihua::mysql_chushihua::chushihua(&peizhi_guanliqii).await {
            Ok(guanliqi) => {
                crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                    "mysql数据库初始化成功啦！",
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
                guanliqi
            }
            Err(e) => {
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &format!("mysql数据库初始化失败: {}", e),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
                return Err(anyhow::anyhow!("mysql数据库初始化失败: {}", e));
            }
        };

        // 6. 初始化Redis数据库
        let redis_guanliqi = match redis_chushihua::redis_chushihua::chushihua(&peizhi_guanliqii) {
            Ok(guanliqi) => {
                crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                    "Redis数据库初始化成功啦！",
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
                guanliqi
            }
            Err(e) => {
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &format!("Redis数据库初始化失败: {}", e),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
                return Err(anyhow::anyhow!("Redis数据库初始化失败: {}", e));
            }
        };
        Ok((peizhi_guanliqii, wangzhanjichuxinxi_guanliqii, mysql_guanliqi, redis_guanliqi))
    }


    /// 简化版初始化（不返回配置管理器）
    pub async fn houduan_chushihua_jiandan() -> Result<()> {
        let (_peizhi_guanliqii, _wangzhanjichuxinxi_guanliqii, _mysql_guanliqi, _redis_guanliqi) = Self::houduan_chushihua_xitong().await?;
        Ok(())
    }



    /// 显示ASCII艺术字
    fn xianshi_ascii_yishuzhi() {
        let ascii_yishuzhi = r#"
            ##                        ###
                                       ##
 ##  ##    ###      ####     ####      ##     ##  ##    ####
  ####      ##         ##   ##  ##     ##     ##  ##   ##  ##
   ##       ##      #####   ##  ##     ##     ##  ##   ##  ##
  ####      ##     ##  ##   ##  ##     ##     ##  ##   ##  ##
 ##  ##    ####     #####    ####     ####     ######   ####
"#;

        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
            ascii_yishuzhi,
            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
        );
    }


    /// 检查端口是否可用
    fn jiancha_duankou_keyong(duankou: u16) -> bool {
        match TcpListener::bind(("0.0.0.0", duankou)) {
            Ok(_) => true,
            Err(_) => false,
        }
    }

    /// 获取随机可用端口
    fn huoqu_suiji_keyong_duankou() -> Result<u16> {
        let mut rng = rand::rng();

        // 最多尝试100次
        for _ in 0..100 {
            // 在1024-65535范围内随机选择端口（避免系统保留端口）
            let duankou: u16 = rng.random_range(1024..=65535);

            if Self::jiancha_duankou_keyong(duankou) {
                return Ok(duankou);
            }
        }

        Err(anyhow::anyhow!("无法找到可用端口，已尝试100次"))
    }
    /// 启动服务器
    pub async fn qidong_fuwuqi() -> Result<()> {
        // 1. 初始化系统并获取配置
        let (peizhi_guanliqii, wangzhanjichuxinxi_guanliqii, mysql_guanliqi, redis_guanliqi) = Self::houduan_chushihua_xitong().await?;

        // 2. 获取配置信息
        let peizhi = peizhi_guanliqii.peizhixitong_get_peizhi()?;
        let mut duankou = peizhi.yunxingduankou;

        // 检查配置的端口是否可用
        if !Self::jiancha_duankou_keyong(duankou) {
            crate::rizhixitong::rizhixitong_jinggao_with_moshi(
                &format!("配置的端口 {} 已被占用，正在选择随机可用端口...", duankou),
                crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
            );

            duankou = Self::huoqu_suiji_keyong_duankou()?;

            crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                &format!("已选择新的可用端口: {}", duankou),
                crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
            );
        }

        // 3. 显示授权信息
        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
            "欢迎使用RO百科资料站程序，正在激活授权：",
            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
        );

        // 等待3秒
        tokio::time::sleep(tokio::time::Duration::from_secs(3)).await;

        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
            "激活成功，到期时间：9999-12-12:12-12（开发者永久授权）",
            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
        );

        // 显示ASCII艺术字
        Self::xianshi_ascii_yishuzhi();

        // 4. 配置 Rocket 服务器
        let config = Config {
            port: duankou,
            address: std::net::Ipv4Addr::new(0, 0, 0, 0).into(),
            log_level: rocket::config::LogLevel::Off,
            cli_colors: false,
            ..Config::default()
        };

        // 5. 启动服务器
        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
            &format!("服务器运行成功，已经成功挂载到端口:[{}]", duankou),
            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
        );

        let mysql_arc = std::sync::Arc::new(mysql_guanliqi);
        let redis_arc = std::sync::Arc::new(redis_guanliqi);
        let wangzhan_arc = std::sync::Arc::new(wangzhanjichuxinxi_guanliqii);
        let rocket = crate::fuwuqi::goujian_fuwuqi_wanzheng(mysql_arc, redis_arc, wangzhan_arc).configure(config);

        // 启动服务器（这会阻塞直到服务器关闭）
        match rocket.launch().await {
            Ok(_) => Ok(()),
            Err(e) => {
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &format!("服务器启动失败: {}", e),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
                );
                Err(anyhow::anyhow!("服务器启动失败: {}", e))
            }
        }
    }
}
