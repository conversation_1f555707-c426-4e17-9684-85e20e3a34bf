import { useState, useCallback, useRef, useEffect } from 'react';
import { useMemoizedFn } from 'ahooks';

/**
 * 预加载状态管理Hook
 * 提供预加载动画的状态控制和API接口
 *
 * @param {Object} options - 配置选项
 * @param {boolean} options.chushizhuangtai - 初始状态，默认为false
 * @param {number} options.zuiduan_xianshi_shijian - 最短显示时间（毫秒），默认500ms
 * @param {number} options.zidong_guanbi_shijian - 自动关闭时间（毫秒），0表示不自动关闭
 * @param {Function} options.onkaishi - 开始预加载时的回调函数
 * @param {Function} options.ontingzhi - 停止预加载时的回调函数
 *
 * @returns {Object} 返回预加载控制对象
 */
const useYujiazai = (options = {}) => {
  const {
    chushizhuangtai = false,
    zuiduan_xianshi_shijian = 500,
    zidong_guanbi_shijian = 0,
    onkaishi,
    ontingzhi
  } = options;

  // 预加载状态
  const [shifouyujiazai, shezhi_shifouyujiazai] = useState(chushizhuangtai);
  
  // 开始时间记录，用于确保最短显示时间
  const kaishi_shijian_ref = useRef(null);
  
  // 自动关闭定时器
  const zidong_guanbi_timer_ref = useRef(null);
  
  // 最短显示时间定时器
  const zuiduan_xianshi_timer_ref = useRef(null);

  // 清理定时器的函数
  const qingli_dinshiqi = useCallback(() => {
    if (zidong_guanbi_timer_ref.current) {
      clearTimeout(zidong_guanbi_timer_ref.current);
      zidong_guanbi_timer_ref.current = null;
    }
    if (zuiduan_xianshi_timer_ref.current) {
      clearTimeout(zuiduan_xianshi_timer_ref.current);
      zuiduan_xianshi_timer_ref.current = null;
    }
  }, []);

  // 开始预加载
  const kaishiyujiazai = useMemoizedFn((peizhi = {}) => {
    const {
      wenzi = '加载中...',
      zidong_guanbi = zidong_guanbi_shijian,
      qiangzhi = false
    } = peizhi;

    // 如果已经在加载中且不是强制开始，则忽略
    if (shifouyujiazai && !qiangzhi) {
      return;
    }

    // 清理之前的定时器
    qingli_dinshiqi();

    // 记录开始时间
    kaishi_shijian_ref.current = Date.now();

    // 设置预加载状态
    shezhi_shifouyujiazai(true);

    // 执行开始回调
    if (onkaishi) {
      onkaishi({ wenzi, zidong_guanbi });
    }

    // 设置自动关闭定时器
    if (zidong_guanbi > 0) {
      zidong_guanbi_timer_ref.current = setTimeout(() => {
        tingzhiyujiazai();
      }, zidong_guanbi);
    }
  });

  // 停止预加载
  const tingzhiyujiazai = useMemoizedFn((peizhi = {}) => {
    const {
      qiangzhi = false,
      yanchi = 0
    } = peizhi;

    // 如果不是强制停止，需要检查最短显示时间
    if (!qiangzhi && kaishi_shijian_ref.current) {
      const yijing_xianshi_shijian = Date.now() - kaishi_shijian_ref.current;
      const shengyu_shijian = zuiduan_xianshi_shijian - yijing_xianshi_shijian;

      if (shengyu_shijian > 0) {
        // 还没达到最短显示时间，延迟关闭
        zuiduan_xianshi_timer_ref.current = setTimeout(() => {
          zhixing_tingzhi(yanchi);
        }, shengyu_shijian);
        return;
      }
    }

    // 立即执行停止或延迟停止
    if (yanchi > 0) {
      setTimeout(() => {
        zhixing_tingzhi(0);
      }, yanchi);
    } else {
      zhixing_tingzhi(0);
    }
  });

  // 执行停止操作的内部函数
  const zhixing_tingzhi = useCallback((yanchi_shijian) => {
    // 清理定时器
    qingli_dinshiqi();

    // 重置开始时间
    kaishi_shijian_ref.current = null;

    // 设置预加载状态为false
    shezhi_shifouyujiazai(false);

    // 执行停止回调
    if (ontingzhi) {
      ontingzhi({ yanchi_shijian });
    }
  }, [qingli_dinshiqi, ontingzhi]);

  // 直接设置预加载状态（不考虑最短显示时间）
  const shezhi_yujiazai_zhuangtai = useMemoizedFn((zhuangtai, peizhi = {}) => {
    if (zhuangtai) {
      kaishiyujiazai({ ...peizhi, qiangzhi: true });
    } else {
      tingzhiyujiazai({ ...peizhi, qiangzhi: true });
    }
  });

  // 切换预加载状态
  const qiehuan_yujiazai_zhuangtai = useMemoizedFn((peizhi = {}) => {
    if (shifouyujiazai) {
      tingzhiyujiazai(peizhi);
    } else {
      kaishiyujiazai(peizhi);
    }
  });

  // 获取当前状态信息
  const huoqu_zhuangtai_xinxi = useMemoizedFn(() => {
    const dangqian_shijian = Date.now();
    const yijing_yunxing_shijian = kaishi_shijian_ref.current 
      ? dangqian_shijian - kaishi_shijian_ref.current 
      : 0;

    return {
      shifouyujiazai,
      kaishi_shijian: kaishi_shijian_ref.current,
      yijing_yunxing_shijian,
      you_zidong_guanbi_dinshiqi: !!zidong_guanbi_timer_ref.current,
      you_zuiduan_xianshi_dinshiqi: !!zuiduan_xianshi_timer_ref.current
    };
  });

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      qingli_dinshiqi();
    };
  }, [qingli_dinshiqi]);

  return {
    // 状态
    shifouyujiazai,
    
    // 控制方法
    kaishiyujiazai,
    tingzhiyujiazai,
    shezhi_yujiazai_zhuangtai,
    qiehuan_yujiazai_zhuangtai,
    
    // 工具方法
    huoqu_zhuangtai_xinxi,
    qingli_dinshiqi
  };
};

export default useYujiazai;
