# 预加载动画组件 (yu<PERSON>az<PERSON><PERSON>jian)

一个具有凄凉美学风格的全屏预加载动画组件，营造深邃宁静的视觉体验。

## 功能特性

- ✨ **全屏覆盖** - 完全覆盖整个屏幕的预加载界面
- � **凄凉美学** - 深蓝色调配合星空效果，营造宁静凄美的氛围
- 📱 **响应式设计** - 完美适配桌面端、平板和移动设备
- 🔄 **流畅动画** - 基于framer-motion的高性能动画效果，节奏缓慢优雅
- ⚡ **灵活控制** - 提供完整的API接口控制预加载状态
- 🎯 **智能管理** - 支持最短显示时间和自动关闭功能
- 🌟 **视觉效果** - 包含星空背景、光晕效果、多层粒子动画
- 👁️ **眼球特效** - 中心眼球独立旋转，营造神秘凝视感
- 🎨 **主题适配** - 自动适配明亮/暗黑主题，呈现不同的凄凉风格

### 主题风格

**暗黑模式**：深蓝色背景 + 天蓝色光效，营造深邃夜空的凄凉感
**明亮模式**：淡紫色背景 + 紫色光效，营造梦幻薄雾的凄凉感

## 安装使用

### 基础使用

```jsx
import React from 'react';
import { yujiazaizujian, useyujiazai } from '../zujian/yujiazaizujian';

function App() {
  const { 
    shifouyujiazai, 
    kaishiyujiazai, 
    tingzhiyujiazai 
  } = useyujiazai();

  const chuli_kaishi_jiazai = () => {
    kaishiyujiazai({
      wenzi: '正在加载数据...',
      zidong_guanbi: 3000 // 3秒后自动关闭
    });
  };

  return (
    <div>
      <button onClick={chuli_kaishi_jiazai}>开始加载</button>
      <button onClick={tingzhiyujiazai}>停止加载</button>
      
      <yujiazaizujian
        xianshi={shifouyujiazai}
        wenzi="加载中..."
        xianshi_jindutiao={true}
      />
    </div>
  );
}
```

### 高级使用示例

```jsx
import React, { useEffect } from 'react';
import { yujiazaizujian, useyujiazai } from '../zujian/yujiazaizujian';

function GaojishiyongApp() {
  const { 
    shifouyujiazai, 
    kaishiyujiazai, 
    tingzhiyujiazai,
    huoqu_zhuangtai_xinxi 
  } = useyujiazai({
    zuiduan_xianshi_shijian: 1000, // 最短显示1秒
    onkaishi: () => console.log('预加载开始'),
    ontingzhi: () => console.log('预加载结束')
  });

  // 模拟数据加载
  const jiazai_shuju = async () => {
    kaishiyujiazai({ wenzi: '正在获取数据...' });
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 2000));
      console.log('数据加载完成');
    } catch (error) {
      console.error('加载失败:', error);
    } finally {
      tingzhiyujiazai();
    }
  };

  return (
    <div>
      <button onClick={jiazai_shuju}>加载数据</button>
      
      <yujiazaizujian
        xianshi={shifouyujiazai}
        wenzi="正在处理您的请求..."
        xianshi_jindutiao={true}
      />
    </div>
  );
}
```

## API 文档

### yujiazaizujian 组件属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `xianshi` | boolean | false | 是否显示预加载动画 |
| `wenzi` | string | '加载中...' | 显示的加载文字 |
| `xianshi_jindutiao` | boolean | true | 是否显示进度条 |

### useyujiazai Hook

#### 配置选项 (options)

```javascript
const options = {
  chushizhuangtai: false,        // 初始状态
  zuiduan_xianshi_shijian: 500,  // 最短显示时间（毫秒）
  zidong_guanbi_shijian: 0,      // 自动关闭时间（毫秒，0表示不自动关闭）
  onkaishi: (info) => {},        // 开始预加载回调
  ontingzhi: (info) => {}        // 停止预加载回调
};
```

#### 返回值

| 属性/方法 | 类型 | 说明 |
|-----------|------|------|
| `shifouyujiazai` | boolean | 当前预加载状态 |
| `kaishiyujiazai` | function | 开始预加载 |
| `tingzhiyujiazai` | function | 停止预加载 |
| `shezhi_yujiazai_zhuangtai` | function | 直接设置预加载状态 |
| `qiehuan_yujiazai_zhuangtai` | function | 切换预加载状态 |
| `huoqu_zhuangtai_xinxi` | function | 获取详细状态信息 |
| `qingli_dinshiqi` | function | 清理所有定时器 |

#### 方法详细说明

**kaishiyujiazai(peizhi)**
```javascript
kaishiyujiazai({
  wenzi: '加载中...',      // 显示文字
  zidong_guanbi: 0,        // 自动关闭时间（毫秒）
  qiangzhi: false          // 是否强制开始（忽略当前状态）
});
```

**tingzhiyujiazai(peizhi)**
```javascript
tingzhiyujiazai({
  qiangzhi: false,         // 是否强制停止（忽略最短显示时间）
  yanchi: 0                // 延迟停止时间（毫秒）
});
```

## 使用场景

### 1. 页面初始化加载
```jsx
useEffect(() => {
  kaishiyujiazai({ wenzi: '正在初始化...' });
  
  // 执行初始化操作
  chushihua().then(() => {
    tingzhiyujiazai();
  });
}, []);
```

### 2. 异步操作提示
```jsx
const chuli_tijiao = async () => {
  kaishiyujiazai({ wenzi: '正在提交数据...' });
  
  try {
    await tijiao_shuju();
    // 成功处理
  } catch (error) {
    // 错误处理
  } finally {
    tingzhiyujiazai();
  }
};
```

### 3. 路由切换加载
```jsx
const { shifouyujiazai, kaishiyujiazai, tingzhiyujiazai } = useyujiazai({
  zuiduan_xianshi_shijian: 300
});

// 在路由切换时使用
useEffect(() => {
  kaishiyujiazai({ wenzi: '正在跳转...' });
  
  setTimeout(() => {
    tingzhiyujiazai();
  }, 1000);
}, [location.pathname]);
```

## 注意事项

1. **性能优化**: 组件使用了`AnimatePresence`确保动画完成后完全卸载
2. **主题适配**: 自动根据当前主题调整颜色和样式
3. **响应式**: 在不同屏幕尺寸下自动调整大小和间距
4. **内存管理**: Hook会自动清理定时器，避免内存泄漏
5. **最短显示时间**: 防止加载动画闪烁，确保用户体验

## 样式定制

组件完全基于主题系统，会自动适配项目的主题配置。如需自定义样式，可以通过修改主题配置文件来实现全局调整。
