import { useState, useEffect, useMemo } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { useMemoizedFn, useResponsive } from 'ahooks';
import { useShiyong<PERSON><PERSON><PERSON> } from '../minganbuju/zhutitiqigong.js';
import <PERSON><PERSON><PERSON><PERSON><PERSON>an from '../minganbuju/zujian_zhutiqiehuan.jsx';
import {
  useYidongduanJiance,
  useZhengming_cebiankuang_guanbi,
  useShebeileixingJiance,
  useDongtaiCaidanPeizhi,
  jisuanCebiankuangKuandu,
  meiti_chaxun
} from '../../gongju/shebeishiPei_gongju.js';

// 导航栏容器
const Daohanglanrongqi = styled(motion.nav)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: ${props => props.theme.yanse.wenzi_zhuyao === '#ffffff'
    ? 'rgba(0, 0, 0, 0.95)'
    : props.theme.yanse.biaomian
  };
  backdrop-filter: blur(15px);
  border-bottom: 1px solid;
  border-bottom-color: ${props => props.theme.yanse.wenzi_zhuyao === '#ffffff'
    ? props.theme.yanse.danjinse_touming
    : props.theme.yanse.danlanse_touming
  };
  transition: background ${props => props.theme.donghua.sujian.zhongdeng} ${props => props.theme.donghua.huanman.biaozhun},
              backdrop-filter ${props => props.theme.donghua.sujian.zhongdeng} ${props => props.theme.donghua.huanman.biaozhun},
              border-bottom-color ${props => props.theme.donghua.sujian.zhongdeng} ${props => props.theme.donghua.huanman.biaozhun};
`;

// 导航栏内容容器
const Daohanglanneirongrq = styled.div`
  width: 100%;
  padding: 0 ${props => props.theme.jianju.zhongdeng};
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: flex-start;

  /* 桌面端优化布局 */
  ${meiti_chaxun.zhuomian} {
    justify-content: space-between;
    max-width: 100%;
    overflow: hidden;
  }

  ${meiti_chaxun.ji_xiao_shouji} {
    justify-content: space-between;
    padding: 0 ${props => props.theme.jianju.ji_xiao};
    height: 48px;
  }

  ${meiti_chaxun.xiao_shouji} {
    justify-content: space-between;
    padding: 0 ${props => props.theme.jianju.xiao};
    height: 52px;
  }

  ${meiti_chaxun.zhongdeng_shouji} {
    justify-content: space-between;
    padding: 0 ${props => props.theme.jianju.xiao};
    height: 56px;
  }

  ${meiti_chaxun.pingban} {
    justify-content: space-between;
    padding: 0 ${props => props.theme.jianju.zhongdeng};
    height: 60px;
  }

  ${meiti_chaxun.xiaoping_zhuomian} {
    justify-content: space-between;
    padding: 0 ${props => props.theme.jianju.zhongdeng};
    height: 60px;
  }
`;

// Logo 区域
const Logoququyu = styled(motion.div)`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.jianju.xiao};
  cursor: pointer;
  transition: all ${props => props.theme.donghua.sujian.kuai} ${props => props.theme.donghua.huanman.biaozhun};

  /* 完全禁用焦点功能 */
  pointer-events: auto;
  -webkit-tap-highlight-color: transparent !important;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  /* 强制移除所有聚焦和激活状态 */
  &:focus,
  &:focus-visible,
  &:focus-within,
  &:active,
  &[tabindex],
  &[tabindex="0"],
  &[tabindex="-1"] {
    outline: none !important;
    box-shadow: none !important;
    background: transparent !important;
    border: none !important;
    transform: none !important;
  }

  /* 移动端强制处理 */
  @media (hover: none) and (pointer: coarse) {
    &:focus,
    &:active,
    &:focus-visible,
    &:focus-within {
      outline: none !important;
      box-shadow: none !important;
      background: transparent !important;
      border: none !important;
      transform: none !important;
    }
  }

  /* 只在非触摸设备上显示hover效果 */
  @media (hover: hover) and (pointer: fine) {
    &:hover {
      opacity: 0.8;
    }
  }
`;

// Logo 图片
const Logotupian = styled.img`
  width: 32px;
  height: 32px;
  border-radius: ${props => props.theme.yuanjiao.zhongdeng};
  object-fit: cover;

  ${meiti_chaxun.ji_xiao_shouji} {
    width: 24px;
    height: 24px;
  }

  ${meiti_chaxun.xiao_shouji} {
    width: 26px;
    height: 26px;
  }

  ${meiti_chaxun.zhongdeng_shouji} {
    width: 28px;
    height: 28px;
  }

  ${meiti_chaxun.pingban} {
    width: 30px;
    height: 30px;
  }
`;

// 网站名称
const Wangzhanmingcheng = styled.h1`
  font-size: ${props => props.theme.ziti.daxiao.da};
  font-weight: ${props => props.theme.ziti.zhongliang.cuhei};
  color: ${props => props.theme.yanse.wenzi_zhuyao};
  margin: 0;
  white-space: nowrap;

  ${meiti_chaxun.ji_xiao_shouji} {
    font-size: ${props => props.theme.ziti.daxiao.ji_xiao};
  }

  ${meiti_chaxun.xiao_shouji} {
    font-size: ${props => props.theme.ziti.daxiao.xiao};
  }

  ${meiti_chaxun.zhongdeng_shouji} {
    font-size: ${props => props.theme.ziti.daxiao.xiao};
  }

  ${meiti_chaxun.pingban} {
    font-size: ${props => props.theme.ziti.daxiao.zhongdeng};
  }

  ${meiti_chaxun.xiaoping_zhuomian} {
    font-size: ${props => props.theme.ziti.daxiao.zhongdeng};
  }
`;

// 桌面端菜单容器
const Zhuomianduancaidan = styled.div.withConfig({
  shouldForwardProp: (prop) => prop !== 'jianju'
})`
  display: flex;
  align-items: center;
  gap: ${props => props.jianju || props.theme.jianju.zhongdeng};
  margin-left: ${props => props.theme.jianju.da};
  flex-wrap: nowrap; /* 防止换行 */
  overflow: hidden; /* 隐藏溢出 */
  flex-shrink: 1; /* 允许收缩 */

  ${meiti_chaxun.shouji} {
    display: none;
  }

  ${meiti_chaxun.pingban} {
    display: none;
  }

  ${meiti_chaxun.xiaoping_zhuomian} {
    display: none;
  }

  /* 小桌面端紧凑布局 */
  ${meiti_chaxun.xiao_zhuomian} {
    gap: 8px;
    margin-left: ${props => props.theme.jianju.zhongdeng};
  }

  /* 中等桌面端正常布局 */
  ${meiti_chaxun.zhongdeng_zhuomian_new} {
    gap: 12px;
    margin-left: ${props => props.theme.jianju.da};
  }

  /* 大桌面端宽松布局 */
  ${meiti_chaxun.da_zhuomian} {
    gap: 16px;
    margin-left: ${props => props.theme.jianju.da};
  }
`;

// 菜单项
const Caidanxiang = styled(motion.a).withConfig({
  shouldForwardProp: (prop) => !['ziti_daxiao', 'padding'].includes(prop)
})`
  position: relative;
  color: ${props => props.theme.yanse.wenzi_zhuyao};
  text-decoration: none;
  font-size: ${props => props.ziti_daxiao || props.theme.ziti.daxiao.zhongdeng};
  font-weight: ${props => props.theme.ziti.zhongliang.zhongdeng};
  padding: ${props => props.padding || `${props.theme.jianju.xiao} ${props.theme.jianju.zhongdeng}`};
  border-radius: ${props => props.theme.yuanjiao.xiao};
  transition: all ${props => props.theme.donghua.sujian.kuai} ${props => props.theme.donghua.huanman.biaozhun};
  cursor: pointer;
  overflow: hidden;
  white-space: nowrap; /* 防止文字换行 */
  flex-shrink: 0; /* 防止收缩 */

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: ${props => props.theme.yanse.beijing_er};
    border-radius: ${props => props.theme.yuanjiao.xiao};
    opacity: 0;
    transform: scale(0.8);
    transition: all ${props => props.theme.donghua.sujian.kuai} ${props => props.theme.donghua.huanman.biaozhun};
    z-index: -1;
  }

  &:hover {
    color: ${props => props.theme.yanse.wenzi_zhuyao};

    &::before {
      opacity: 1;
      transform: scale(1);
    }
  }

  &.active {
    color: ${props => props.theme.yanse.wenzi_zhuyao === '#ffffff'
      ? props.theme.yanse.danjinse
      : props.theme.yanse.danlanse
    };
    cursor: default !important;
    opacity: 1;
    pointer-events: none;

    &::before {
      opacity: 1;
      transform: scale(1);
      background: ${props => props.theme.yanse.wenzi_zhuyao === '#ffffff'
        ? `linear-gradient(135deg,
            ${props.theme.yanse.danjinse}15 0%,
            ${props.theme.yanse.danjinse}25 50%,
            ${props.theme.yanse.danjinse}15 100%)`
        : `linear-gradient(135deg,
            ${props.theme.yanse.danlanse}15 0%,
            ${props.theme.yanse.danlanse}25 50%,
            ${props.theme.yanse.danlanse}15 100%)`
      };
    }

    /* 禁用hover效果 */
    &:hover {
      transform: none !important;
      opacity: 1 !important;
    }
  }

  /* 小桌面端紧凑样式 */
  ${meiti_chaxun.xiao_zhuomian} {
    font-size: 13px;
    padding: 6px 10px;
  }

  /* 中等桌面端正常样式 */
  ${meiti_chaxun.zhongdeng_zhuomian_new} {
    font-size: 14px;
    padding: 8px 12px;
  }

  /* 大桌面端宽松样式 */
  ${meiti_chaxun.da_zhuomian} {
    font-size: 14px;
    padding: 8px 16px;
  }
`;

// 弹性空间
const Tanxingkongjian = styled.div`
  flex-grow: 1;
  min-width: 0; /* 允许收缩 */

  /* 桌面端确保有足够空间 */
  ${meiti_chaxun.zhuomian} {
    flex-grow: 1;
    flex-shrink: 1;
    min-width: 20px; /* 最小宽度 */
  }
`;

// 右侧操作区域
const Youcecaozuoqu = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.jianju.zhongdeng};
`;


// 桌面端主题切换容器
const Zhuomianduanzhutiqiehuan = styled.div`
  ${meiti_chaxun.shouji} {
    display: none;
  }

  ${meiti_chaxun.pingban} {
    display: none;
  }

  ${meiti_chaxun.xiaoping_zhuomian} {
    display: none;
  }
`;

// 移动端菜单按钮
const Yidongduancaidananniu = styled(motion.button)`
  display: none;
  background: none;
  border: none;
  color: ${props => props.theme.yanse.wenzi_zhuyao};
  cursor: pointer;
  padding: ${props => props.theme.jianju.xiao};
  border-radius: ${props => props.theme.yuanjiao.xiao};
  transition: all ${props => props.theme.donghua.sujian.kuai} ${props => props.theme.donghua.huanman.biaozhun};

  /* 完全禁用焦点功能 */
  pointer-events: auto;
  -webkit-tap-highlight-color: transparent !important;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  /* 强制移除所有聚焦和激活状态 */
  &:focus,
  &:focus-visible,
  &:focus-within,
  &:active,
  &[tabindex],
  &[tabindex="0"],
  &[tabindex="-1"] {
    outline: none !important;
    box-shadow: none !important;
    background: transparent !important;
    border: none !important;
    transform: none !important;
  }

  /* 移动端强制处理 */
  @media (hover: none) and (pointer: coarse) {
    &:focus,
    &:active,
    &:focus-visible,
    &:focus-within {
      outline: none !important;
      box-shadow: none !important;
      background: transparent !important;
      border: none !important;
      transform: none !important;
    }
  }

  &:hover {
    background: ${props => props.theme.yanse.beijing_er};
  }

  ${meiti_chaxun.ji_xiao_shouji} {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    font-size: 16px;
  }

  ${meiti_chaxun.xiao_shouji} {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 38px;
    height: 38px;
    font-size: 18px;
  }

  ${meiti_chaxun.zhongdeng_shouji} {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    font-size: 20px;
  }

  ${meiti_chaxun.pingban} {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  ${meiti_chaxun.xiaoping_zhuomian} {
    display: flex;
    align-items: center;
    justify-content: center;
  }
`;

// 移动端侧边栏
const Yidongduancebiankuang = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  width: ${props => props.kuandu || '50vw'};
  height: 100vh;
  background: ${props => props.theme.yanse.wenzi_zhuyao === '#ffffff'
    ? 'rgba(0, 0, 0, 0.98)'
    : props.theme.yanse.biaomian
  };
  backdrop-filter: blur(20px);
  border-right: 1px solid ${props => props.theme.yanse.biankuang};
  box-shadow: ${props => props.theme.yinying.da};
  z-index: 1001;
  padding: ${props => props.theme.jianju.da} ${props => props.theme.jianju.zhongdeng} ${props => props.theme.jianju.xiao} ${props => props.theme.jianju.zhongdeng};
  overflow-y: auto;
  display: flex;
  flex-direction: column;

  /* 屏蔽手机端点击高亮效果 */
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  ${meiti_chaxun.ji_xiao_shouji} {
    width: 100vw;
  }

  ${meiti_chaxun.xiao_shouji} {
    width: 85vw;
  }

  ${meiti_chaxun.zhongdeng_shouji} {
    width: 75vw;
  }

  ${meiti_chaxun.pingban} {
    width: 40vw;
    min-width: 300px;
    max-width: 400px;
  }
`;

// 移动端菜单头部
const Yidongduancaidantoubu = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${props => props.theme.jianju.zhongdeng};
  padding-bottom: ${props => props.theme.jianju.zhongdeng};
  border-bottom: 1px solid ${props => props.theme.yanse.biankuang};
`;

// 侧边栏关闭按钮
const Cebiankuangguanbi = styled(motion.button)`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: ${props => props.theme.yuanjiao.xiao};
  background: ${props => props.theme.yanse.beijing_er};
  color: ${props => props.theme.yanse.wenzi_zhuyao};
  cursor: pointer;
  font-size: 18px;
  line-height: 1;
  transition: all 0.2s ease;

  /* 屏蔽手机端点击高亮效果 */
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  &:hover {
    background: ${props => props.theme.yanse.beijing_san};
    transform: scale(1.05);
  }

  &:active {
    transform: scale(0.95);
  }

  /* 只在全屏模式下显示关闭按钮 */
  ${meiti_chaxun.ji_xiao_shouji} {
    display: flex;
  }

  ${meiti_chaxun.xiao_shouji} {
    display: flex;
  }

  /* 中等手机屏幕和更大屏幕都隐藏关闭按钮（因为不是全屏，有遮罩层可以点击关闭） */
  ${meiti_chaxun.zhongdeng_shouji} {
    display: none;
  }

  ${meiti_chaxun.pingban} {
    display: none;
  }

  ${meiti_chaxun.zhuomian} {
    display: none;
  }
`;

// 移动端侧边栏Logo图片
const Yidongduancebiankuanglogotupian = styled.img`
  width: 40px;
  height: 40px;
  border-radius: ${props => props.theme.yuanjiao.zhongdeng};
  object-fit: cover;

  ${meiti_chaxun.ji_xiao_shouji} {
    width: 32px;
    height: 32px;
  }

  ${meiti_chaxun.xiao_shouji} {
    width: 36px;
    height: 36px;
  }
`;

// 移动端侧边栏网站名称
const Yidongduancebiankuangwangzhanmingcheng = styled.h1`
  font-size: ${props => props.theme.ziti.daxiao.da};
  font-weight: ${props => props.theme.ziti.zhongliang.cuhei};
  color: ${props => props.theme.yanse.wenzi_zhuyao};
  margin: 0;
  white-space: nowrap;

  ${meiti_chaxun.ji_xiao_shouji} {
    font-size: ${props => props.theme.ziti.daxiao.zhongdeng};
  }

  ${meiti_chaxun.xiao_shouji} {
    font-size: ${props => props.theme.ziti.daxiao.zhongdeng};
  }
`;




// 移动端菜单项
const Yidongduancaidanxiang = styled(motion.a)`
  color: ${props => props.theme.yanse.wenzi_ciyao};
  text-decoration: none;
  font-size: ${props => props.theme.ziti.daxiao.zhongdeng};
  font-weight: ${props => props.theme.ziti.zhongliang.zhongdeng};
  padding: ${props => props.theme.jianju.zhongdeng};
  margin-bottom: 2px;

  /* 小屏幕设备增加触摸区域 */
  ${meiti_chaxun.ji_xiao_shouji} {
    padding: ${props => props.theme.jianju.da};
    font-size: ${props => props.theme.ziti.daxiao.xiao};
    margin-bottom: 4px;
  }

  ${meiti_chaxun.xiao_shouji} {
    padding: ${props => props.theme.jianju.da};
    font-size: ${props => props.theme.ziti.daxiao.zhongdeng};
    margin-bottom: 3px;
  }
  border-radius: ${props => props.theme.yuanjiao.zhongdeng};
  transition: all ${props => props.theme.donghua.sujian.kuai} ${props => props.theme.donghua.huanman.biaozhun};
  cursor: pointer;
  position: relative;
  overflow: hidden;

  /* 完全禁用焦点功能 */
  pointer-events: auto;
  -webkit-tap-highlight-color: transparent !important;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  /* 强制移除所有聚焦和激活状态 */
  &:focus,
  &:focus-visible,
  &:focus-within,
  &:active,
  &[tabindex],
  &[tabindex="0"],
  &[tabindex="-1"] {
    outline: none !important;
    box-shadow: none !important;
    background: transparent !important;
    border: none !important;
    transform: none !important;
  }

  /* 移动端强制处理 */
  @media (hover: none) and (pointer: coarse) {
    &:focus,
    &:active,
    &:focus-visible,
    &:focus-within {
      outline: none !important;
      box-shadow: none !important;
      background: transparent !important;
      border: none !important;
      transform: none !important;
    }
  }

  /* 渐变背景效果 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: transparent;
    border-radius: ${props => props.theme.yuanjiao.zhongdeng};
    opacity: 0;
    transform: scale(0.8);
    transition: all ${props => props.theme.donghua.sujian.zhongdeng} ${props => props.theme.donghua.huanman.biaozhun};
    z-index: -1;
  }

  &:hover {
    color: ${props => props.theme.yanse.wenzi_zhuyao};

    &::before {
      opacity: 0.5;
      transform: scale(1);
      background: ${props => props.theme.yanse.beijing_er};
    }
  }

  &.active {
    color: ${props => props.theme.yanse.wenzi_zhuyao === '#ffffff'
      ? props.theme.yanse.danjinse
      : props.theme.yanse.danlanse
    };
    cursor: default !important;
    opacity: 1;
    pointer-events: none;

    &::before {
      opacity: 1;
      transform: scale(1);
      background: ${props => props.theme.yanse.wenzi_zhuyao === '#ffffff'
        ? `linear-gradient(135deg,
            ${props.theme.yanse.danjinse}15 0%,
            ${props.theme.yanse.danjinse}25 50%,
            ${props.theme.yanse.danjinse}15 100%)`
        : `linear-gradient(135deg,
            ${props.theme.yanse.danlanse}15 0%,
            ${props.theme.yanse.danlanse}25 50%,
            ${props.theme.yanse.danlanse}15 100%)`
      };
    }

    /* 禁用hover效果 */
    &:hover {
      transform: none !important;
      opacity: 1 !important;

      &::before {
        opacity: 1 !important;
      }
    }
  }
`;

// 移动端底部操作区域
const Yidongduandibucaozuoqu = styled.div`
  margin-top: auto;
  padding-top: ${props => props.theme.jianju.xiao};
  border-top: 1px solid ${props => props.theme.yanse.biankuang};
`;

// 移动端登录链接
const Yidongduandenglulianjie = styled(motion.a)`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.jianju.xiao};
  padding: ${props => props.theme.jianju.zhongdeng};
  color: ${props => props.theme.yanse.wenzi_ciyao};
  text-decoration: none;
  font-size: ${props => props.theme.ziti.daxiao.zhongdeng};
  font-weight: ${props => props.theme.ziti.zhongliang.zhongdeng};
  border-radius: ${props => props.theme.yuanjiao.zhongdeng};
  transition: all ${props => props.theme.donghua.sujian.kuai} ${props => props.theme.donghua.huanman.biaozhun};
  cursor: pointer;
  margin-bottom: 2px;
  position: relative;
  overflow: hidden;

  /* 完全禁用焦点功能 */
  pointer-events: auto;
  -webkit-tap-highlight-color: transparent !important;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  /* 强制移除所有聚焦和激活状态 */
  &:focus,
  &:focus-visible,
  &:focus-within,
  &:active,
  &[tabindex],
  &[tabindex="0"],
  &[tabindex="-1"] {
    outline: none !important;
    box-shadow: none !important;
    background: transparent !important;
    border: none !important;
    transform: none !important;
  }

  /* 移动端强制处理 */
  @media (hover: none) and (pointer: coarse) {
    &:focus,
    &:active,
    &:focus-visible,
    &:focus-within {
      outline: none !important;
      box-shadow: none !important;
      background: transparent !important;
      border: none !important;
      transform: none !important;
    }
  }

  /* 渐变背景效果 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: transparent;
    border-radius: ${props => props.theme.yuanjiao.zhongdeng};
    opacity: 0;
    transform: scale(0.8);
    transition: all ${props => props.theme.donghua.sujian.zhongdeng} ${props => props.theme.donghua.huanman.biaozhun};
    z-index: -1;
  }

  &:hover {
    color: ${props => props.theme.yanse.wenzi_zhuyao};

    &::before {
      opacity: 0.5;
      transform: scale(1);
      background: ${props => props.theme.yanse.beijing_er};
    }
  }
`;

// 移动端主题切换区域
const Yidongduanzhutiqiehuanqu = styled.div`
  padding-top: 2px;
`;

// 主题切换选项
const Zhutiqiehuanxuanxiang = styled(motion.div)`
  display: flex;
  align-items: center;
  padding: ${props => props.theme.jianju.zhongdeng};
  margin-bottom: 2px;
  border-radius: ${props => props.theme.yuanjiao.zhongdeng};
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all ${props => props.theme.donghua.sujian.kuai} ${props => props.theme.donghua.huanman.biaozhun};

  /* 完全禁用焦点功能 */
  pointer-events: auto;
  -webkit-tap-highlight-color: transparent !important;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  /* 强制移除所有聚焦和激活状态 */
  &:focus,
  &:focus-visible,
  &:focus-within,
  &:active,
  &[tabindex],
  &[tabindex="0"],
  &[tabindex="-1"] {
    outline: none !important;
    box-shadow: none !important;
    background: transparent !important;
    border: none !important;
    transform: none !important;
  }

  /* 移动端强制处理 */
  @media (hover: none) and (pointer: coarse) {
    &:focus,
    &:active,
    &:focus-visible,
    &:focus-within {
      outline: none !important;
      box-shadow: none !important;
      background: transparent !important;
      border: none !important;
      transform: none !important;
    }
  }

  /* 渐变背景效果 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: transparent;
    border-radius: ${props => props.theme.yuanjiao.zhongdeng};
    opacity: 0;
    transform: scale(0.8);
    transition: all ${props => props.theme.donghua.sujian.zhongdeng} ${props => props.theme.donghua.huanman.biaozhun};
    z-index: -1;
  }

  &:hover {
    &::before {
      opacity: 0.5;
      transform: scale(1);
      background: ${props => props.theme.yanse.beijing_er};
    }
  }
`;

// 主题切换文字
const Zhutiqiehuanwenzi = styled.span`
  color: ${props => props.theme.yanse.wenzi_ciyao};
  font-size: ${props => props.theme.ziti.daxiao.zhongdeng};
  font-weight: ${props => props.theme.ziti.zhongliang.zhongdeng};
`;

// 主题切换图标
const Zhutiqiehuantubiao = styled(motion.div)`
  width: 20px;
  height: 20px;
  margin-right: ${props => props.theme.jianju.xiao};
  color: ${props => props.$isanhei
    ? props.theme.yanse.danjinse
    : props.theme.yanse.danlanse
  };
  transition: color ${props => props.theme.donghua.sujian.zhongdeng} ${props => props.theme.donghua.huanman.biaozhun};

  svg {
    width: 100%;
    height: 100%;
    fill: currentColor;
  }
`;

// 遮罩层
const Zhezhaoceng = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  backdrop-filter: blur(2px);
`;

// 动画配置
const cebiankuangdonghhua = {
  hidden: {
    x: '-100%',
    boxShadow: 'none',
    borderRightColor: 'transparent',
    transition: {
      duration: 0.3,
      ease: 'easeInOut'
    }
  },
  visible: {
    x: 0,
    transition: {
      duration: 0.3,
      ease: 'easeInOut'
    }
  }
};

const zhezhaocengdonghhua = {
  hidden: {
    opacity: 0,
    transition: {
      duration: 0.3,
      ease: 'easeInOut'
    }
  },
  visible: {
    opacity: 1,
    transition: {
      duration: 0.2,
      ease: 'easeInOut'
    }
  }
};

// 登录图标组件
const Denglutubiao = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
    <motion.path
      d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      initial={{ pathLength: 0 }}
      animate={{ pathLength: 1 }}
      transition={{ duration: 0.5, ease: "easeInOut" }}
    />
    <motion.path
      d="M10 17l5-5-5-5"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      initial={{ pathLength: 0 }}
      animate={{ pathLength: 1 }}
      transition={{ duration: 0.5, delay: 0.2, ease: "easeInOut" }}
    />
    <motion.path
      d="M15 12H3"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      initial={{ pathLength: 0 }}
      animate={{ pathLength: 1 }}
      transition={{ duration: 0.5, delay: 0.4, ease: "easeInOut" }}
    />
  </svg>
);

// 汉堡菜单图标组件
const Hanbaobaocaidantubiao = ({ isopen }) => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
    <motion.path
      d={isopen ? "M6 18L18 6" : "M3 12h18"}
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      animate={{ d: isopen ? "M6 18L18 6" : "M3 12h18" }}
      transition={{ duration: 0.2 }}
    />
    <motion.path
      d={isopen ? "M6 6l12 12" : "M3 6h18"}
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      animate={{ 
        d: isopen ? "M6 6l12 12" : "M3 6h18",
        opacity: isopen ? 1 : 1
      }}
      transition={{ duration: 0.2 }}
    />
    <motion.path
      d="M3 18h18"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      animate={{ opacity: isopen ? 0 : 1 }}
      transition={{ duration: 0.2 }}
    />
  </svg>
);

/**
 * 顶部导航栏组件
 * @param {string} wangzhanmingcheng - 网站名称
 * @param {string} wangzhanlogo - 网站Logo URL
 * @param {Array} caidanxiangmu - 菜单项数组 [{mingcheng: '首页', lianjie: '/', huoyue: true}]
 * @param {boolean} xianshi - 是否显示导航栏
 */
const Dingbudaohanglan = ({
  wangzhanmingcheng = '网站名称',
  wangzhanlogo = '',
  caidanxiangmu = [],
  xianshi = true
}) => {
  const [cebiankuangkaiqii, shezhi_cebiankuangkaiqii] = useState(false);
  const { dangqianzhuti, qiehuandaoxiayigezhuti } = useShiyongzhuti();
  const responsive = useResponsive();

  // 判断是否为暗黑主题
  const shifoianheizuti = dangqianzhuti === 'anhei';

  // 构建完整的菜单项数组，包含内置的首页链接
  const wanzheng_caidanxiangmu = useMemo(() => {
    // 检查当前页面路径
    const dangqian_lujing = window.location.pathname;
    const shouye_huoyue = dangqian_lujing === '/' || dangqian_lujing === '';

    // 创建首页菜单项
    const shouye_xiangmu = {
      mingcheng: '首页',
      lianjie: '/',
      huoyue: shouye_huoyue
    };

    // 为外部传入的菜单项设置活跃状态
    const chuli_caidanxiangmu = caidanxiangmu.map(xiangmu => ({
      ...xiangmu,
      huoyue: dangqian_lujing === xiangmu.lianjie
    }));

    // 将首页菜单项放在最前面，然后是处理过的外部菜单项
    return [shouye_xiangmu, ...chuli_caidanxiangmu];
  }, [caidanxiangmu]);

  // 构建桌面端菜单项数组，包含登录链接
  const zhuomian_caidanxiangmu = useMemo(() => {
    // 检查当前页面是否为登录页
    const dangqian_lujing = window.location.pathname;
    const denglu_huoyue = dangqian_lujing === '/denglu';

    // 创建登录/注册菜单项（仅用于桌面端）
    const denglu_xiangmu = {
      mingcheng: '登录/注册',
      lianjie: '/denglu',
      huoyue: denglu_huoyue
    };

    // 桌面端菜单包含所有项目，包括登录链接
    return [...wanzheng_caidanxiangmu, denglu_xiangmu];
  }, [wanzheng_caidanxiangmu]);

  // 处理菜单项点击
  const chuli_caidanxiang_dianji = useMemoizedFn((lianjie, event, shifoihuoyue = false) => {
    // 如果是活跃菜单项，阻止所有导航行为
    if (shifoihuoyue) {
      event.preventDefault();
      event.stopPropagation();
      // 移除焦点状态
      if (event && event.currentTarget && typeof event.currentTarget.blur === 'function') {
        try {
          event.currentTarget.blur();
        } catch (e) {
          // 忽略blur错误
        }
      }
      return;
    }

    if (lianjie) {
      // 检查是否是当前页面，如果是则强制刷新
      if (window.location.href === lianjie || window.location.pathname === lianjie) {
        window.location.reload();
      } else {
        window.location.href = lianjie;
      }
    }
    shezhi_cebiankuangkaiqii(false);
    // 移除焦点状态，防止手机端持续聚焦
    if (event && event.currentTarget && typeof event.currentTarget.blur === 'function') {
      try {
        event.currentTarget.blur();
      } catch (e) {
        // 忽略blur错误
      }
    }
  });

  // 使用设备适配模块
  const { jiance_yidongduan } = useYidongduanJiance();
  const { zhengming_cebiankuang_guanbi } = useZhengming_cebiankuang_guanbi();
  const { shuchu_tiaoshi_xinxi, huoqu_shebeileixing } = useShebeileixingJiance();
  const { huoqu_caidanpeizhi } = useDongtaiCaidanPeizhi(zhuomian_caidanxiangmu.length);

  // 在开发环境下输出设备检测调试信息
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      shuchu_tiaoshi_xinxi();
    }
  }, [shuchu_tiaoshi_xinxi]);

  // 动态菜单配置
  const [caidanpeizhi, shezhi_caidanpeizhi] = useState(() => huoqu_caidanpeizhi());

  // 动态侧边栏配置
  const [cebiankuangpeizhi, shezhi_cebiankuangpeizhi] = useState(() => {
    const shebeileixing = huoqu_shebeileixing();
    const kuandu = window.innerWidth;
    return jisuanCebiankuangKuandu(shebeileixing, kuandu);
  });

  // 监听窗口大小变化，更新菜单配置和侧边栏配置
  useEffect(() => {
    const chuli_chuangkou_bianhua = () => {
      const xin_peizhi = huoqu_caidanpeizhi();
      shezhi_caidanpeizhi(xin_peizhi);

      const shebeileixing = huoqu_shebeileixing();
      const kuandu = window.innerWidth;
      const xin_cebiankuangpeizhi = jisuanCebiankuangKuandu(shebeileixing, kuandu);
      shezhi_cebiankuangpeizhi(xin_cebiankuangpeizhi);
    };

    window.addEventListener('resize', chuli_chuangkou_bianhua);
    return () => window.removeEventListener('resize', chuli_chuangkou_bianhua);
  }, [huoqu_caidanpeizhi, huoqu_shebeileixing]);

  // 处理logo点击
  const chuli_logo_dianji = useMemoizedFn((event) => {
    // 在移动端环境下阻止页面刷新
    if (jiance_yidongduan()) {
      event.preventDefault();
      event.stopPropagation();
      // 移除焦点状态
      if (event && event.currentTarget && typeof event.currentTarget.blur === 'function') {
        try {
          event.currentTarget.blur();
        } catch (e) {
          // 忽略blur错误
        }
      }
      return;
    }

    // 桌面端保持原有行为：强制刷新到首页
    window.location.href = '/';
    // 移除焦点状态
    if (event && event.currentTarget && typeof event.currentTarget.blur === 'function') {
      try {
        event.currentTarget.blur();
      } catch (e) {
        // 忽略blur错误
      }
    }
  });

  // 切换侧边栏
  const qiehuan_cebiankuang = useMemoizedFn((event) => {
    shezhi_cebiankuangkaiqii(!cebiankuangkaiqii);
    // 移除焦点状态，防止手机端持续聚焦
    if (event && event.currentTarget && typeof event.currentTarget.blur === 'function') {
      try {
        event.currentTarget.blur();
      } catch (e) {
        // 忽略blur错误
      }
    }
  });

  // 关闭侧边栏
  const guanbi_cebiankuang = useMemoizedFn(() => {
    shezhi_cebiankuangkaiqii(false);
  });

  // 处理主题切换
  const chuli_zhutiqiehuan = useMemoizedFn((event) => {
    qiehuandaoxiayigezhuti();
    // 移除焦点状态，防止手机端持续聚焦
    if (event && event.currentTarget && typeof event.currentTarget.blur === 'function') {
      try {
        event.currentTarget.blur();
      } catch (e) {
        // 忽略blur错误
      }
    }
  });

  // 处理触摸结束，强制移除焦点
  const chuli_chumojieshu = useMemoizedFn((event) => {
    if (event && event.currentTarget && typeof event.currentTarget.blur === 'function') {
      setTimeout(() => {
        try {
          if (event.currentTarget && typeof event.currentTarget.blur === 'function') {
            event.currentTarget.blur();
          }
        } catch (e) {
          // 忽略blur错误
        }
      }, 100);
    }
  });

  // 处理触摸开始事件，阻止默认行为
  const chuli_chumokaishi = useMemoizedFn((event) => {
    // 检查是否为被动监听器，如果不是则阻止默认行为
    try {
      // 只在事件可取消时才调用preventDefault
      if (event.cancelable && !event.defaultPrevented) {
        event.preventDefault();
      }
      // 阻止事件冒泡，避免触发父元素的事件
      event.stopPropagation();
    } catch (e) {
      // 忽略preventDefault错误，这在被动监听器中是正常的
      console.debug('preventDefault failed in passive listener:', e.message);
    }
  });

  // 监听窗口大小变化，智能关闭侧边栏
  useEffect(() => {
    if (zhengming_cebiankuang_guanbi(responsive, cebiankuangkaiqii)) {
      shezhi_cebiankuangkaiqii(false);
    }
  }, [responsive?.xl, cebiankuangkaiqii]);

  // 阻止页面滚动（当侧边栏打开时）
  useEffect(() => {
    if (cebiankuangkaiqii) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [cebiankuangkaiqii]);

  if (!xianshi) {
    return null;
  }

  return (
    <>
      <Daohanglanrongqi
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.3, ease: 'easeOut' }}
      >
        <Daohanglanneirongrq>
          {/* Logo 区域 */}
          <Logoququyu
            onClick={(e) => chuli_logo_dianji(e)}
            onTouchEnd={chuli_chumojieshu}
            onTouchStart={chuli_chumokaishi}
            onMouseDown={(e) => e.preventDefault()}
            tabIndex={-1}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            {wangzhanlogo && (
              <Logotupian src={wangzhanlogo} alt={wangzhanmingcheng} />
            )}
            <Wangzhanmingcheng>{wangzhanmingcheng}</Wangzhanmingcheng>
          </Logoququyu>

          {/* 桌面端菜单 */}
          <Zhuomianduancaidan jianju={caidanpeizhi.jianju}>
            {zhuomian_caidanxiangmu.map((xiangmu, suoyin) => (
              <Caidanxiang
                key={suoyin}
                className={xiangmu.huoyue ? 'active' : ''}
                onClick={(e) => chuli_caidanxiang_dianji(xiangmu.lianjie, e, xiangmu.huoyue)}
                whileHover={xiangmu.huoyue ? {} : { scale: 1.05 }}
                whileTap={xiangmu.huoyue ? {} : { scale: 0.95 }}
                ziti_daxiao={caidanpeizhi.ziti_daxiao}
                padding={caidanpeizhi.padding}
                style={{
                  cursor: xiangmu.huoyue ? 'default' : 'pointer',
                  opacity: 1
                }}
              >
                {xiangmu.mingcheng}
              </Caidanxiang>
            ))}
          </Zhuomianduancaidan>

          <Tanxingkongjian />

          {/* 右侧操作区域 */}
          <Youcecaozuoqu>
            {/* 桌面端主题切换 */}
            <Zhuomianduanzhutiqiehuan>
              <ZhutiqiehuanZujian size={40} />
            </Zhuomianduanzhutiqiehuan>

            {/* 移动端菜单按钮 */}
            <Yidongduancaidananniu
              onClick={(e) => qiehuan_cebiankuang(e)}
              onTouchEnd={chuli_chumojieshu}
              onTouchStart={chuli_chumokaishi}
              onMouseDown={(e) => e.preventDefault()}
              tabIndex={-1}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Hanbaobaocaidantubiao isopen={cebiankuangkaiqii} />
            </Yidongduancaidananniu>
          </Youcecaozuoqu>
        </Daohanglanneirongrq>
      </Daohanglanrongqi>

      {/* 移动端侧边栏和遮罩 */}
      <AnimatePresence mode="wait">
        {cebiankuangkaiqii && (
          <>
            <Zhezhaoceng
              variants={zhezhaocengdonghhua}
              initial="hidden"
              animate="visible"
              exit="hidden"
              onClick={guanbi_cebiankuang}
            />
            <Yidongduancebiankuang
              variants={cebiankuangdonghhua}
              initial="hidden"
              animate="visible"
              exit="hidden"
              kuandu={cebiankuangpeizhi.kuandu}
            >
              <Yidongduancaidantoubu>
                <Logoququyu
                  onClick={(e) => chuli_logo_dianji(e)}
                  onTouchEnd={chuli_chumojieshu}
                  onTouchStart={chuli_chumokaishi}
                  onMouseDown={(e) => e.preventDefault()}
                  tabIndex={-1}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  {wangzhanlogo && (
                    <Yidongduancebiankuanglogotupian src={wangzhanlogo} alt={wangzhanmingcheng} />
                  )}
                  <Yidongduancebiankuangwangzhanmingcheng style={{ display: 'block' }}>
                    {wangzhanmingcheng}
                  </Yidongduancebiankuangwangzhanmingcheng>
                </Logoququyu>

                {cebiankuangpeizhi.xianshi_guanbi && (
                  <Cebiankuangguanbi
                    onClick={guanbi_cebiankuang}
                    onTouchEnd={chuli_chumojieshu}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    aria-label="关闭侧边栏"
                  >
                    ✕
                  </Cebiankuangguanbi>
                )}
              </Yidongduancaidantoubu>

              {wanzheng_caidanxiangmu.map((xiangmu, suoyin) => (
                <Yidongduancaidanxiang
                  key={suoyin}
                  className={xiangmu.huoyue ? 'active' : ''}
                  onClick={(e) => chuli_caidanxiang_dianji(xiangmu.lianjie, e, xiangmu.huoyue)}
                  onTouchEnd={chuli_chumojieshu}
                  onTouchStart={chuli_chumokaishi}
                  onMouseDown={(e) => e.preventDefault()}
                  tabIndex={-1}
                  whileHover={xiangmu.huoyue ? {} : { x: 8 }}
                  whileTap={xiangmu.huoyue ? {} : { scale: 0.98 }}
                  style={{
                    cursor: xiangmu.huoyue ? 'default' : 'pointer',
                    opacity: 1
                  }}
                >
                  {xiangmu.mingcheng}
                </Yidongduancaidanxiang>
              ))}

              {/* 移动端底部操作区域 */}
              <Yidongduandibucaozuoqu>
                {/* 移动端登录链接 */}
                <Yidongduandenglulianjie
                  href="/denglu"
                  onTouchEnd={chuli_chumojieshu}
                  onTouchStart={chuli_chumokaishi}
                  onMouseDown={(e) => e.preventDefault()}
                  tabIndex={-1}
                  whileHover={{ x: 4 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Denglutubiao />
                  登录/注册
                </Yidongduandenglulianjie>

                {/* 移动端主题切换区域 */}
                <Yidongduanzhutiqiehuanqu>
                <Zhutiqiehuanxuanxiang
                  onClick={(e) => chuli_zhutiqiehuan(e)}
                  onTouchEnd={chuli_chumojieshu}
                  onTouchStart={chuli_chumokaishi}
                  onMouseDown={(e) => e.preventDefault()}
                  tabIndex={-1}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Zhutiqiehuantubiao
                    $isanhei={shifoianheizuti}
                    key={shifoianheizuti ? 'moon' : 'sun'}
                    initial={{ scale: 0, rotate: -180 }}
                    animate={{ scale: 1, rotate: 0 }}
                    exit={{ scale: 0, rotate: 180 }}
                    transition={{
                      duration: 0.3,
                      ease: "easeInOut",
                      scale: { type: "spring", stiffness: 200, damping: 15 }
                    }}
                  >
                    {shifoianheizuti ? (
                      // 月亮图标（深色模式）
                      <motion.svg
                        viewBox="0 0 24 24"
                        fill="none"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 0.1 }}
                      >
                        <motion.path
                          d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"
                          fill="currentColor"
                          initial={{ pathLength: 0, opacity: 0 }}
                          animate={{ pathLength: 1, opacity: 1 }}
                          transition={{ duration: 0.5, delay: 0.6, ease: "easeInOut" }}
                        />
                      </motion.svg>
                    ) : (
                      // 太阳图标（浅色模式）
                      <motion.svg
                        viewBox="0 0 24 24"
                        fill="none"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 0.1 }}
                      >
                        <motion.circle
                          cx="12"
                          cy="12"
                          r="5"
                          fill="currentColor"
                          initial={{ scale: 0, opacity: 0 }}
                          animate={{ scale: 1, opacity: 1 }}
                          transition={{ duration: 0.5, delay: 0.6, ease: "easeInOut" }}
                        />
                        {/* 太阳光线动画 */}
                        {[
                          { x1: "12", y1: "1", x2: "12", y2: "3" },
                          { x1: "12", y1: "21", x2: "12", y2: "23" },
                          { x1: "4.22", y1: "4.22", x2: "5.64", y2: "5.64" },
                          { x1: "18.36", y1: "18.36", x2: "19.78", y2: "19.78" },
                          { x1: "1", y1: "12", x2: "3", y2: "12" },
                          { x1: "21", y1: "12", x2: "23", y2: "12" },
                          { x1: "4.22", y1: "19.78", x2: "5.64", y2: "18.36" },
                          { x1: "18.36", y1: "5.64", x2: "19.78", y2: "4.22" }
                        ].map((line, index) => (
                          <motion.line
                            key={index}
                            x1={line.x1}
                            y1={line.y1}
                            x2={line.x2}
                            y2={line.y2}
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            initial={{ pathLength: 0, opacity: 0 }}
                            animate={{ pathLength: 1, opacity: 1 }}
                            transition={{
                              duration: 0.5,
                              delay: 0.8 + index * 0.1,
                              ease: "easeInOut"
                            }}
                          />
                        ))}
                      </motion.svg>
                    )}
                  </Zhutiqiehuantubiao>
                  <Zhutiqiehuanwenzi>
                    {shifoianheizuti ? '浅色模式' : '深色模式'}
                  </Zhutiqiehuanwenzi>
                </Zhutiqiehuanxuanxiang>
                </Yidongduanzhutiqiehuanqu>
              </Yidongduandibucaozuoqu>
            </Yidongduancebiankuang>
          </>
        )}
      </AnimatePresence>
    </>
  );
};

export default Dingbudaohanglan;
