import { useState, useEffect } from 'react';
import { useMemoizedFn } from 'ahooks';

/**
 * 路由检测 Hook
 * 用于检测当前页面路径，判断是否需要显示顶部导航栏
 * @returns {Object} 包含当前路径和是否需要显示导航栏的信息
 */
export const useLuyoujiance = () => {
  // 获取当前路径
  const huoqudangqianlujing = useMemoizedFn(() => {
    return window.location.pathname;
  });

  // 状态管理
  const [dangqianlujing, shezhi_dangqianlujing] = useState(huoqudangqianlujing);

  // 检查是否为需要显示导航栏的路由
  const shifougengluyou = useMemoizedFn((lujing) => {
    // 定义需要显示导航栏的路由列表
    const xianshi_daohanglan_luyou = [
      '/',
      '',
      '/guaiwushuju',
      '/wupinshuju',
      '/ditushuju',
      '/jinengshuju'
    ];

    return xianshi_daohanglan_luyou.includes(lujing);
  });

  // 处理路径变化
  const chuli_lujing_bianhua = useMemoizedFn(() => {
    const xinlujing = huoqudangqianlujing();
    if (xinlujing !== dangqianlujing) {
      shezhi_dangqianlujing(xinlujing);
    }
  });

  // 监听浏览器历史记录变化
  useEffect(() => {
    // 监听 popstate 事件（浏览器前进后退）
    const chuli_popstate = () => {
      chuli_lujing_bianhua();
    };

    // 监听 pushstate 和 replacestate 事件
    const yuanshi_pushstate = window.history.pushState;
    const yuanshi_replacestate = window.history.replaceState;

    window.history.pushState = function(...args) {
      yuanshi_pushstate.apply(window.history, args);
      chuli_lujing_bianhua();
    };

    window.history.replaceState = function(...args) {
      yuanshi_replacestate.apply(window.history, args);
      chuli_lujing_bianhua();
    };

    // 添加事件监听器
    window.addEventListener('popstate', chuli_popstate);

    // 初始化时检查一次
    chuli_lujing_bianhua();

    // 清理函数
    return () => {
      window.removeEventListener('popstate', chuli_popstate);
      // 恢复原始方法
      window.history.pushState = yuanshi_pushstate;
      window.history.replaceState = yuanshi_replacestate;
    };
  }, [chuli_lujing_bianhua]);

  // 返回路由信息
  return {
    dangqianlujing,
    shifougengluyou: shifougengluyou(dangqianlujing),
    huoqudangqianlujing,
    shifougengluyou_hanshu: shifougengluyou
  };
};

/**
 * 简化版路由检测 Hook
 * 只返回是否需要显示导航栏的布尔值
 * @returns {boolean} 是否需要显示导航栏
 */
export const useShifougengluyou = () => {
  const { shifougengluyou } = useLuyoujiance();
  return shifougengluyou;
};

export default useLuyoujiance;
