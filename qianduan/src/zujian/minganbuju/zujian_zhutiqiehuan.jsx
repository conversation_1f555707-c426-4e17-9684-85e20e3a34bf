import { useMemo } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { BsSun, BsMoon } from 'react-icons/bs';
import { useMemoizedFn, useLatest } from 'ahooks';
import { useShiyon<PERSON><PERSON><PERSON><PERSON> } from './zhutitiqigong.js';
import { zhutileixing } from './zhutipeizhi.js';

// 主题切换按钮容器
const ZhutiqiehuanRongqi = styled(motion.button)`
  position: relative;
  width: ${props => props.$size || 48}px;
  height: ${props => props.$size || 48}px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  background: ${props => props.theme.yanse.beijing_er};
  color: ${props => props.theme.yanse.wenzi_zhuyao};
  box-shadow: ${props => props.theme.yinying.xiao};
  transition: all 0.2s ease;
  overflow: hidden;

  /* 移动端触摸优化 */
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;

  &:hover {
    transform: scale(1.05);
    box-shadow: ${props => props.theme.yinying.zhongdeng};
    background: ${props => props.theme.yanse.beijing_san};
  }

  &:active {
    transform: scale(0.95);
  }

  &:focus {
    outline: none;
  }

  /* 移动端完全禁用焦点样式，避免触摸后出现焦点边框 */
  &:focus:not(:focus-visible) {
    box-shadow: ${props => props.theme.yinying.xiao};
    outline: none;
    border: none;
  }

  /* 仅在键盘导航时显示焦点指示 */
  &:focus-visible {
    outline: none;
    box-shadow: ${props => props.theme.yinying.zhongdeng},
                0 0 0 2px ${props => props.theme.yanse.zhuyao}40;
  }

  /* 强制移除所有可能的焦点样式 */
  &:focus,
  &:active,
  &:focus:active {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
`;

// 图标容器
const TubiaorRongqi = styled(motion.div)`
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  font-size: ${props => props.$iconsize || 20}px;
`;

// 太阳图标样式
const TaiyangtTubiao = styled(BsSun)`
  color: ${props => props.theme.yanse.zhuyao};
`;

// 月亮图标样式
const YueliangtTubiao = styled(BsMoon)`
  color: ${props => props.theme.yanse.zhuyao};
`;

// 动画变体配置
const tubiaodonghhua = {
  // 太阳图标动画
  taiyangxianshi: {
    opacity: 1,
    rotate: 0,
    scale: 1,
    transition: {
      duration: 0.3,
      ease: "easeInOut"
    }
  },
  taiyangyincang: {
    opacity: 0,
    rotate: 180,
    scale: 0.8,
    transition: {
      duration: 0.3,
      ease: "easeInOut"
    }
  },

  // 月亮图标动画
  yueliangxianshi: {
    opacity: 1,
    rotate: 0,
    scale: 1,
    transition: {
      duration: 0.3,
      ease: "easeInOut"
    }
  },
  yueliangyincang: {
    opacity: 0,
    rotate: -180,
    scale: 0.8,
    transition: {
      duration: 0.3,
      ease: "easeInOut"
    }
  }
};

// 按钮悬停动画
const anniudonghhua = {
  hover: {
    scale: 1.05,
    transition: {
      duration: 0.2,
      ease: "easeInOut"
    }
  },
  tap: {
    scale: 0.95,
    transition: {
      duration: 0.1,
      ease: "easeInOut"
    }
  }
};

/**
 * 主题切换组件
 * 提供明亮/暗黑主题之间的一键切换功能
 * @param {number} size - 组件尺寸（像素），默认48px
 */
const ZhutiqiehuanZujian = ({ size = 48 }) => {
  const { dangqianzhuti, qiehuandaoxiayigezhuti } = useShiyongzhuti();

  // 使用 useLatest 确保在异步操作中使用最新的值
  const zuixinqiehuanhanshu = useLatest(qiehuandaoxiayigezhuti);

  // 判断当前是否为暗黑主题
  const shifoianheizuti = useMemo(() =>
    dangqianzhuti === zhutileixing.anhei,
    [dangqianzhuti]
  );

  // 计算图标尺寸（约为按钮尺寸的60%）
  const tubiaochicun = useMemo(() =>
    Math.max(16, Math.floor(size * 0.6)),
    [size]
  );

  // 使用 useMemoizedFn 优化主题切换处理函数
  const chuliqiehuanzhuti = useMemoizedFn((event) => {
    // 使用最新的切换函数
    zuixinqiehuanhanshu.current();

    // 移动端优化：强制移除焦点，避免焦点状态持续显示
    const target = event.currentTarget || event.target;
    if (target && typeof target.blur === 'function') {
      // 使用 setTimeout 确保在事件处理完成后移除焦点
      setTimeout(() => {
        target.blur();
        // 额外确保移除焦点
        if (document.activeElement === target) {
          document.activeElement.blur();
        }
      }, 0);
    }
  });

  // 使用 useMemoizedFn 优化触摸结束事件处理函数
  const chulichumomojieshu = useMemoizedFn((event) => {
    const target = event.currentTarget || event.target;
    if (target && typeof target.blur === 'function') {
      setTimeout(() => target.blur(), 10);
    }
  });

  return (
    <ZhutiqiehuanRongqi
      $size={size}
      onClick={chuliqiehuanzhuti}
      onTouchEnd={chulichumomojieshu}
      variants={anniudonghhua}
      whileHover="hover"
      whileTap="tap"
      aria-label={shifoianheizuti ? "切换到明亮主题" : "切换到暗黑主题"}
    >
      {/* 太阳图标 - 在明亮主题时显示 */}
      <TubiaorRongqi
        $iconsize={tubiaochicun}
        variants={tubiaodonghhua}
        animate={!shifoianheizuti ? "taiyangxianshi" : "taiyangyincang"}
        initial={false}
      >
        <TaiyangtTubiao />
      </TubiaorRongqi>

      {/* 月亮图标 - 在暗黑主题时显示 */}
      <TubiaorRongqi
        $iconsize={tubiaochicun}
        variants={tubiaodonghhua}
        animate={shifoianheizuti ? "yueliangxianshi" : "yueliangyincang"}
        initial={false}
      >
        <YueliangtTubiao />
      </TubiaorRongqi>
    </ZhutiqiehuanRongqi>
  );
};

export default ZhutiqiehuanZujian;