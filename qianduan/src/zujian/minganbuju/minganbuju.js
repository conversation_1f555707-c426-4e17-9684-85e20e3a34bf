import React from 'react';
import styled, { createGlobalStyle } from 'styled-components';
import { Zhutitiqigong } from './zhutitiqigong.js';

// 全局样式
const Quanjuyangshi = createGlobalStyle`
  * {
    box-sizing: border-box;
  }

  html {
    scroll-behavior: smooth;
    background: ${props => props.theme.yanse.beijing};
    min-height: 100%;
  }

  body {
    margin: 0;
    padding: 0;
    font-family: ${props => props.theme.ziti.jiazu};
    background: ${props => props.theme.yanse.beijing};
    color: ${props => props.theme.yanse.wenzi_zhuyao};
    min-height: 100vh;
  }

  #root {
    min-height: 100vh;
    background: ${props => props.theme.yanse.beijing};
    position: relative;
  }
  
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    background: ${props => props.theme.yanse.beijing_er};
  }
  
  ::-webkit-scrollbar-thumb {
    background: ${props => props.theme.yanse.biankuang};
    border-radius: ${props => props.theme.yuanjiao.xiao};
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: ${props => props.theme.yanse.biankuang_qian};
  }
`;

// 主容器样式
const Zhurognqi = styled.div`
  width: 100%;
  min-height: 100vh;
  background: ${props => props.theme.yanse.beijing};
  color: ${props => props.theme.yanse.wenzi_zhuyao};
  position: relative;
  transition: all ${props => props.theme.donghua.sujian.zhongdeng} ${props => props.theme.donghua.huanman.biaozhun};

`;

// 内容区域样式
const Neirongquyu = styled.div`
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
`;

// 明暗布局组件内部实现
const Minganbujuneibu = ({
  children,
  qiyongquanjuyangshi = true,
  zidingyiyangshi = {},
  className,
  ...qitashuxing
}) => {
  return (
    <>
      {qiyongquanjuyangshi && <Quanjuyangshi />}
      <Zhurognqi
        className={className}
        style={zidingyiyangshi}
        {...qitashuxing}
      >
        <Neirongquyu>
          {children}
        </Neirongquyu>
      </Zhurognqi>
    </>
  );
};

// 明暗布局主组件
export const Minganbuju = ({
  children,
  chushipzhuti,
  qiyongquanjuyangshi,
  zidingyiyangshi,
  className,
  ...qitashuxing
}) => {
  return (
    <Zhutitiqigong chushipzhuti={chushipzhuti}>
      <Minganbujuneibu
        qiyongquanjuyangshi={qiyongquanjuyangshi}
        zidingyiyangshi={zidingyiyangshi}
        className={className}
        {...qitashuxing}
      >
        {children}
      </Minganbujuneibu>
    </Zhutitiqigong>
  );
};

// 明暗布局包装器（用于已有 ThemeProvider 的应用）
export const Minganbujubaoguoqi = ({
  children,
  qiyongquanjuyangshi = true,
  zidingyiyangshi = {},
  className,
  ...qitashuxing
}) => {
  return (
    <>
      {qiyongquanjuyangshi && <Quanjuyangshi />}
      <Zhurognqi
        className={className}
        style={zidingyiyangshi}
        {...qitashuxing}
      >
        <Neirongquyu>
          {children}
        </Neirongquyu>
      </Zhurognqi>
    </>
  );
};
