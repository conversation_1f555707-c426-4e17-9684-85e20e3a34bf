/**
 * 明暗布局组件错误管理模块
 * 集中管理所有硬编码的错误消息和字符串常量
 */

// 本地存储键名常量
export const bencunchu<PERSON><PERSON><PERSON>an = {
  zhuti_cunchu_jian: 'minganbuju_zhuti',
  zidong_qiehuan_cunchu_jian: 'minganbuju_zidong_qiehuan',
};

// 错误消息常量
export const cuowuxiaoxi = {
  // 主题相关错误
  zhuti_weixiaohua: 'useShiyongzhuti 必须在 Zhutitiqigong 内部使用',
  zhuti_buzhichi: '不支持的主题类型',
  zhuti_jiazaishibai: '主题加载失败',
  
  // 动画相关错误
  donghua_peizhi_wuxiao: '动画配置无效',
  donghua_leixing_buzhichi: '不支持的动画类型',
  donghua_sudu_wuxiao: '动画速度配置无效',
  
  // 存储相关错误
  bencunchuduqushibai: '本地存储读取失败',
  bencunchucunchushibai: '本地存储保存失败',
  bencunchushujuwuxiao: '本地存储数据无效',
  
  // 系统相关错误
  liulanqibuzhichi: '浏览器不支持此功能',
  xitongzhutijiance_shibai: '系统主题检测失败',
  meiti_chaxun_shibai: '媒体查询失败',
  
  // 组件相关错误
  zujian_chuanshuwuxiao: '组件传参无效',
  zujian_chushihua_shibai: '组件初始化失败',
  zujian_xuanran_shibai: '组件渲染失败',
};

// 提示信息常量
export const tishixinxi = {
  // 主题切换提示
  zhuti_qiehuan_chenggong: '主题切换成功',
  zhuti_qiehuan_shibai: '主题切换失败',
  zhuti_huifu_moren: '已恢复默认主题',
  
  // 快捷键提示
  kuaijiejian_tishi: '使用 Ctrl+Shift+T 快捷键快速切换主题',
  kuaijiejian_chufaichenggong: '快捷键触发成功',
  
  // 自动切换提示
  zidong_qiehuan_qiyong: '已启用自动主题切换',
  zidong_qiehuan_guanbi: '已关闭自动主题切换',
  xitong_zhuti_bianhua: '检测到系统主题变化',
  
  // 存储提示
  shuju_yichunchu: '数据已存储到本地',
  shuju_yihuifu: '数据已从本地恢复',
  huancun_yiqingchu: '缓存已清除',
};

// 调试信息常量
export const tiaoshixinxi = {
  // 组件生命周期
  zujian_jiazai: '明暗布局组件加载',
  zujian_xiaohui: '明暗布局组件销毁',
  zujian_gengxin: '明暗布局组件更新',
  
  // 主题操作
  zhuti_chushihua: '主题初始化',
  zhuti_qiehuan_kaishi: '开始切换主题',
  zhuti_qiehuan_wancheng: '主题切换完成',
  zhuti_peizhi_jiazai: '主题配置加载',
  
  // 动画操作
  donghua_kaishi: '动画开始',
  donghua_jieshu: '动画结束',
  donghua_peizhi_jiazai: '动画配置加载',
  
  // 存储操作
  cunchu_caozuo_kaishi: '存储操作开始',
  cunchu_caozuo_wancheng: '存储操作完成',
  duqu_caozuo_kaishi: '读取操作开始',
  duqu_caozuo_wancheng: '读取操作完成',
};

// 默认值常量
export const morenzhi = {
  // 主题默认值
  moren_zhuti: 'mingliang',
  moren_donghua_leixing: 'danrudanchu',
  moren_donghua_sudu: 'zhongdeng',
  moren_donghua_fangxiang: 'zhongxin',
  
  // 动画默认值
  moren_donghua_shijian: 300,
  moren_yanchi_shijian: 150,
  moren_huanman_hanshu: [0.4, 0, 0.2, 1],
  
  // 存储默认值
  moren_youxiaoqi: 2 * 60 * 60 * 1000, // 2小时
  moren_zidong_qiehuan: false,
};

// 配置常量
export const peizhi = {
  // 动画配置
  donghua_moshi: 'wait',
  donghua_jianjian: 'wait',

  // 主题配置
  zhuti_qianzhui: 'minganbuju',
  css_bianliang_qianzhui: '--',

  // 存储配置
  cunchu_qianzhui: 'minganbuju_',
  shuju_geshi: 'json',

  // 调试配置
  tiaoshi_moshi: false,
  rizhi_jibei: 'info',

  // 媒体查询字符串
  meiti_chaxun_anheimoshi: '(prefers-color-scheme: dark)',
};

// 快捷键配置
export const kuaijiejian = {
  zhuti_qiehuan: 'T', // Ctrl/Cmd + Shift + T
};

// 工具函数：生成带前缀的存储键名
export const shengcheng_cunchu_jian = (jianjian) => {
  return `${peizhi.cunchu_qianzhui}${jianjian}`;
};

// 工具函数：生成CSS变量名
export const shengcheng_css_bianliang = (bianliangjian) => {
  return `${peizhi.css_bianliang_qianzhui}${bianliangjian}`;
};

// 工具函数：格式化错误消息
export const geshihua_cuowu_xiaoxi = (cuowuleixing, xiangxi = '') => {
  const jiben_xiaoxi = cuowuxiaoxi[cuowuleixing] || '未知错误';
  return xiangxi ? `${jiben_xiaoxi}: ${xiangxi}` : jiben_xiaoxi;
};

// 工具函数：格式化调试信息
export const geshihua_tiaoshi_xinxi = (caozuoleixing, xiangxi = '') => {
  const jiben_xinxi = tiaoshixinxi[caozuoleixing] || '未知操作';
  return xiangxi ? `[${jiben_xinxi}] ${xiangxi}` : `[${jiben_xinxi}]`;
};

// 工具函数：检查浏览器环境
export const jianche_liulanqi_huanjing = () => {
  return typeof window !== 'undefined';
};

// 工具函数：检查媒体查询支持
export const jianche_meiti_chaxun_zhichi = () => {
  return jianche_liulanqi_huanjing() && window.matchMedia;
};

// 工具函数：获取系统主题偏好
export const huoqu_xitong_zhuti_pianhaoo = () => {
  if (!jianche_meiti_chaxun_zhichi()) {
    return morenzhi.moren_zhuti;
  }

  const xitongianheimoshi = window.matchMedia(peizhi.meiti_chaxun_anheimoshi).matches;
  return xitongianheimoshi ? 'anhei' : 'mingliang';
};

// 工具函数：检查是否为暗黑主题
export const jianche_shifo_anheizuti = (zhutimingcheng) => {
  return zhutimingcheng === 'anhei';
};

// 工具函数：检查是否为明亮主题
export const jianche_shifo_mingliangzhuti = (zhutimingcheng) => {
  return zhutimingcheng === 'mingliang';
};

// 导出所有常量的集合
export default {
  bencunchujianjian,
  cuowuxiaoxi,
  tishixinxi,
  tiaoshixinxi,
  morenzhi,
  peizhi,
  shengcheng_cunchu_jian,
  shengcheng_css_bianliang,
  geshihua_cuowu_xiaoxi,
  geshihua_tiaoshi_xinxi,
};
