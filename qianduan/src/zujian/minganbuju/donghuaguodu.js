
import { motion, AnimatePresence } from 'framer-motion';
import styled from 'styled-components';

// 动画类型枚举
export const donghualeixin = {
  danrudanchu: 'danrudanchu',
  huadong: 'huadong',
  suofang: 'suofang',
  xuanzhuan: 'xuanzhuan',
  tanxing: 'tanxing',
  bolan: 'bolan',
  silie: 'silie', // 撕裂效果
};

// 动画方向枚举
export const donghuafangxiang = {
  shang: 'shang',
  xia: 'xia',
  zuo: 'zuo',
  you: 'you',
  zhongxin: 'zhongxin',
};

// 动画速度枚举
export const donghuasudu = {
  kuai: 'kuai',
  zhongdeng: 'zhongdeng',
  man: 'man',
  chaoman: 'chaoman',
};

// 动画配置映射
const donghuapeizhi_yingshe = {
  // 淡入淡出动画
  [donghualeixin.danrudanchu]: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
  },

  // 滑动动画
  [donghualeixin.huadong]: {
    [donghuafangxiang.shang]: {
      initial: { opacity: 0, y: 20 },
      animate: { opacity: 1, y: 0 },
      exit: { opacity: 0, y: -20 },
    },
    [donghuafangxiang.xia]: {
      initial: { opacity: 0, y: -20 },
      animate: { opacity: 1, y: 0 },
      exit: { opacity: 0, y: 20 },
    },
    [donghuafangxiang.zuo]: {
      initial: { opacity: 0, x: 20 },
      animate: { opacity: 1, x: 0 },
      exit: { opacity: 0, x: -20 },
    },
    [donghuafangxiang.you]: {
      initial: { opacity: 0, x: -20 },
      animate: { opacity: 1, x: 0 },
      exit: { opacity: 0, x: 20 },
    },
  },

  // 缩放动画
  [donghualeixin.suofang]: {
    initial: { opacity: 0, scale: 0.9 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 1.1 },
  },

  // 旋转动画
  [donghualeixin.xuanzhuan]: {
    initial: { opacity: 0, rotate: -10 },
    animate: { opacity: 1, rotate: 0 },
    exit: { opacity: 0, rotate: 10 },
  },

  // 弹性动画
  [donghualeixin.tanxing]: {
    initial: { opacity: 0, scale: 0.8 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.8 },
  },

  // 波浪动画
  [donghualeixin.bolan]: {
    initial: { opacity: 0, scaleX: 0.8, scaleY: 1.2 },
    animate: { opacity: 1, scaleX: 1, scaleY: 1 },
    exit: { opacity: 0, scaleX: 1.2, scaleY: 0.8 },
  },

  // 撕裂效果动画（从右上角到左下角）
  [donghualeixin.silie]: {
    initial: {
      opacity: 0,
      clipPath: 'polygon(100% 0%, 100% 0%, 100% 0%, 100% 0%)',
      transformOrigin: 'top right'
    },
    animate: {
      opacity: 1,
      clipPath: 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)',
      transformOrigin: 'top right'
    },
    exit: {
      opacity: 0,
      clipPath: 'polygon(0% 0%, 0% 0%, 0% 100%, 0% 100%)',
      transformOrigin: 'top right'
    },
  },
};

// 速度配置映射
const sudupeizhiyingshe = {
  [donghuasudu.kuai]: { duration: 0.15 },
  [donghuasudu.zhongdeng]: { duration: 0.3 },
  [donghuasudu.man]: { duration: 0.5 },
  [donghuasudu.chaoman]: { duration: 0.8 },
};

// 缓动函数映射
const huanmanhanshuyingshe = {
  [donghuasudu.kuai]: [0.4, 0, 1, 1],
  [donghuasudu.zhongdeng]: [0.4, 0, 0.2, 1],
  [donghuasudu.man]: [0.4, 0, 0.6, 1],
  [donghuasudu.chaoman]: [0.25, 0.46, 0.45, 0.94],
};

// 样式化容器
const Donghuarongqi = styled(motion.div)`
  width: 100%;
  height: 100%;
`;

// 主题切换动画组件
export const Zhutiqiehuandonghua = ({
  children,
  donghualeixin: leixing = donghualeixin.danrudanchu,
  donghuafangxiang: fangxiang = donghuafangxiang.zhongxin,
  donghuasudu: sudu = donghuasudu.zhongdeng,
  zidingyipeizhi = {},
  jianjian = 'wait',
  ...qitashuxing
}) => {
  // 获取动画配置
  const huoqudonghuapeizhi = () => {
    let jichupeihi;

    if (leixing === donghualeixin.huadong) {
      jichupeihi = donghuapeizhi_yingshe[leixing][fangxiang] || donghuapeizhi_yingshe[leixing][donghuafangxiang.shang];
    } else {
      jichupeihi = donghuapeizhi_yingshe[leixing] || donghuapeizhi_yingshe[donghualeixin.danrudanchu];
    }

    const sudupeihi = sudupeizhiyingshe[sudu] || sudupeizhiyingshe[donghuasudu.zhongdeng];
    const huanmanhansu = huanmanhanshuyingshe[sudu] || huanmanhanshuyingshe[donghuasudu.zhongdeng];
    
    return {
      ...jichupeihi,
      transition: {
        ...sudupeihi,
        ease: huanmanhansu,
        ...zidingyipeizhi.transition,
      },
      ...zidingyipeizhi,
    };
  };

  const donghuapeizhi = huoqudonghuapeizhi();

  return (
    <AnimatePresence mode={jianjian}>
      <Donghuarongqi
        {...donghuapeizhi}
        {...qitashuxing}
      >
        {children}
      </Donghuarongqi>
    </AnimatePresence>
  );
};

// 主题过渡包装器
export const Zhutiguodubaoguoqi = ({
  children,
  zhutijian,
  donghualeixin: leixing = donghualeixin.danrudanchu,
  donghuafangxiang: fangxiang = donghuafangxiang.zhongxin,
  donghuasudu: sudu = donghuasudu.zhongdeng,
  ...qitashuxing
}) => {
  return (
    <AnimatePresence mode="wait">
      <Zhutiqiehuandonghua
        key={zhutijian}
        donghualeixin={leixing}
        donghuafangxiang={fangxiang}
        donghuasudu={sudu}
        {...qitashuxing}
      >
        {children}
      </Zhutiqiehuandonghua>
    </AnimatePresence>
  );
};

// 高级动画组件 - 支持多层动画
export const Gaojidonghuazujian = ({
  children,
  donghualiebiao = [
    { leixing: donghualeixin.danrudanchu, yanchi: 0 },
  ],
  ...qitashuxing
}) => {
  return (
    <AnimatePresence mode="wait">
      {donghualiebiao.map((donghua, suoyin) => {
        const { leixing, fangxiang, sudu, yanchi = 0, ...donghuapeizhi } = donghua;
        
        return (
          <Zhutiqiehuandonghua
            key={suoyin}
            donghualeixin={leixing}
            donghuafangxiang={fangxiang}
            donghuasudu={sudu}
            zidingyipeizhi={{
              transition: {
                delay: yanchi,
                ...donghuapeizhi.transition,
              },
              ...donghuapeizhi,
            }}
            {...qitashuxing}
          >
            {suoyin === donghualiebiao.length - 1 ? children : null}
          </Zhutiqiehuandonghua>
        );
      })}
    </AnimatePresence>
  );
};

// 主题撕裂切换组件
export const Zhutisiliequiehuan = ({
  children,
  zhutijian,
  donghuasudu: sudu = donghuasudu.zhongdeng,
  ...qitashuxing
}) => {
  const sudupeihi = sudupeizhiyingshe[sudu] || sudupeizhiyingshe[donghuasudu.zhongdeng];
  const huanmanhansu = huanmanhanshuyingshe[sudu] || huanmanhanshuyingshe[donghuasudu.zhongdeng];

  return (
    <AnimatePresence mode="wait">
      <motion.div
        key={zhutijian}
        initial={{
          clipPath: 'polygon(100% 0%, 100% 0%, 100% 0%, 100% 0%)',
        }}
        animate={{
          clipPath: 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)',
        }}
        exit={{
          clipPath: 'polygon(0% 0%, 0% 0%, 0% 100%, 0% 100%)',
        }}
        transition={{
          duration: sudupeihi.duration,
          ease: huanmanhansu,
        }}
        style={{
          width: '100%',
          height: '100%',
          position: 'relative',
        }}
        {...qitashuxing}
      >
        {children}
      </motion.div>
    </AnimatePresence>
  );
};
