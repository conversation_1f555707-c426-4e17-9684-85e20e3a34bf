// 主题配置文件
// 定义明亮和暗黑两套主题的所有样式变量

// 明亮主题配置
export const mingliang<PERSON><PERSON><PERSON> = {
  // 基础颜色
  yanse: {
    // 主要颜色
    zhuyao: '#1976d2',
    zhuyao_qian: '#1565c0',
    zhuya<PERSON>_hou: '#42a5f5',
    
    // 次要颜色
    ciyao: '#dc004e',
    ciyao_qian: '#c51162',
    ciyao_hou: '#f06292',
    danlanse_touming: 'rgba(0, 123, 255, 0.2)',

    // 淡蓝色（浅色模式选中状态）
    danlanse: '#5dade2',
    danlanse_qian: '#3498db',
    danlanse_hou: '#85c1e9',
    
    // 明亮风格背景颜色（温暖柔和的色调）
    beijing: 'linear-gradient(135deg, #ffffff 0%, #fefefe 25%, #fdfdfd 50%, #fcfcfc 75%, #ffffff 100%)',
    beijing_er: 'linear-gradient(45deg, #fafbfc 0%, #f5f7fa 30%, #f0f3f7 60%, #fafbfc 100%)',
    beijing_san: 'linear-gradient(90deg, #f8fafc 0%, #f1f5f9 50%, #f8fafc 100%)',

    // 明亮辅助背景（用于动画效果，使用更柔和的蓝色调）
    mingliang_beijing_1: 'radial-gradient(ellipse at top, rgba(59, 130, 246, 0.05) 0%, transparent 50%)',
    mingliang_beijing_2: 'radial-gradient(ellipse at bottom left, rgba(16, 185, 129, 0.04) 0%, transparent 50%)',
    mingliang_beijing_3: 'radial-gradient(ellipse at bottom right, rgba(245, 158, 11, 0.04) 0%, transparent 50%)',
    mingliang_beijing_4: 'radial-gradient(ellipse at center, rgba(139, 92, 246, 0.03) 0%, transparent 60%)',

    // 表面颜色（带温暖明亮效果）
    biaomian: 'linear-gradient(135deg, #ffffff 0%, #fefefe 50%, #ffffff 100%)',
    biaomian_er: 'linear-gradient(45deg, #fafbfc 0%, #f5f7fa 50%, #fafbfc 100%)',
    
    // 文字颜色（更柔和的深色调）
    wenzi_zhuyao: '#1f2937',
    wenzi_ciyao: '#6b7280',
    wenzi_jinzhi: '#9ca3af',
    wenzi_tishi: '#d1d5db',
    
    // 边框颜色（更精致的边框）
    biankuang: '#e5e7eb',
    biankuang_qian: '#d1d5db',
    
    // 状态颜色（更现代的状态色）
    chenggong: '#10b981',
    jinggao: '#f59e0b',
    cuowu: '#ef4444',
    xinxi: '#3b82f6',
    
    // 阴影颜色（更柔和的阴影）
    yinying: 'rgba(0, 0, 0, 0.08)',
    yinying_qian: 'rgba(0, 0, 0, 0.16)',
  },
  
  // 字体配置
  ziti: {
    jiazu: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    daxiao: {
      xiaoxiao: '12px',
      xiao: '14px',
      zhongdeng: '16px',
      da: '18px',
      dada: '24px',
      chaoda: '32px',
    },
    zhongliang: {
      xichang: 300,
      putong: 400,
      zhongdeng: 500,
      cuhei: 600,
      hei: 700,
    },
    xinggao: {
      jinmi: 1.2,
      putong: 1.5,
      kuansong: 1.8,
    },
  },
  
  // 间距配置
  jianju: {
    xiaoxiao: '4px',
    xiao: '8px',
    zhongdeng: '16px',
    da: '24px',
    dada: '32px',
    chaoda: '48px',
  },
  
  // 圆角配置
  yuanjiao: {
    xiao: '4px',
    zhongdeng: '8px',
    da: '12px',
    yuan: '50%',
  },
  
  // 过渡动画配置
  donghua: {
    sujian: {
      kuai: '0.15s',
      zhongdeng: '0.3s',
      man: '0.5s',
    },
    huanman: {
      jinru: 'cubic-bezier(0.4, 0, 0.2, 1)',
      tuichu: 'cubic-bezier(0.4, 0, 1, 1)',
      biaozhun: 'cubic-bezier(0.4, 0, 0.6, 1)',
    },
  },
  
  // 阴影配置（更柔和精致的阴影）
  yinying: {
    xiao: '0 1px 2px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1)',
    zhongdeng: '0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06)',
    da: '0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05)',
    chaoda: '0 20px 25px rgba(0, 0, 0, 0.15), 0 10px 10px rgba(0, 0, 0, 0.04)',
  },
};

// 暗黑主题配置
export const anheizuti = {
  // 基础颜色
  yanse: {
    // 主要颜色
    zhuyao: '#90caf9',
    zhuyao_qian: '#64b5f6',
    zhuyao_hou: '#bbdefb',
    
    // 次要颜色
    ciyao: '#f48fb1',
    ciyao_qian: '#f06292',
    ciyao_hou: '#ffc1cc',
    danjinse_touming: 'rgba(255, 215, 0, 0.2)',

    // 淡金色（用于选中状态）
    danjinse: '#ffd700',
    danjinse_qian: '#ffed4e',
    danjinse_hou: '#fff176',
    
    // 纯黑色背景
    beijing: '#000000',
    beijing_er: '#000000',
    beijing_san: '#000000',

    // 极光辅助背景（用于动画效果）
    jiguang_beijing_1: 'radial-gradient(ellipse at top, rgba(138, 43, 226, 0.15) 0%, transparent 50%)',
    jiguang_beijing_2: 'radial-gradient(ellipse at bottom left, rgba(0, 255, 127, 0.1) 0%, transparent 50%)',
    jiguang_beijing_3: 'radial-gradient(ellipse at bottom right, rgba(30, 144, 255, 0.1) 0%, transparent 50%)',
    jiguang_beijing_4: 'radial-gradient(ellipse at center, rgba(255, 20, 147, 0.08) 0%, transparent 60%)',

    // 表面颜色（纯黑色）
    biaomian: '#000000',
    biaomian_er: '#111111',
    
    // 文字颜色
    wenzi_zhuyao: '#ffffff',
    wenzi_ciyao: '#cccccc',
    wenzi_jinzhi: '#888888',
    wenzi_tishi: '#aaaaaa',
    
    // 边框颜色
    biankuang: '#404040',
    biankuang_qian: '#666666',
    
    // 状态颜色
    chenggong: '#66bb6a',
    jinggao: '#ffb74d',
    cuowu: '#ef5350',
    xinxi: '#42a5f5',
    
    // 阴影颜色
    yinying: 'rgba(0, 0, 0, 0.5)',
    yinying_qian: 'rgba(0, 0, 0, 0.8)',
  },
  
  // 字体配置（与明亮主题相同）
  ziti: mingliangzhuti.ziti,
  
  // 间距配置（与明亮主题相同）
  jianju: mingliangzhuti.jianju,
  
  // 圆角配置（与明亮主题相同）
  yuanjiao: mingliangzhuti.yuanjiao,
  
  // 过渡动画配置（与明亮主题相同）
  donghua: mingliangzhuti.donghua,
  
  // 阴影配置（暗黑主题专用）
  yinying: {
    xiao: '0 1px 3px rgba(0, 0, 0, 0.5), 0 1px 2px rgba(0, 0, 0, 0.6)',
    zhongdeng: '0 3px 6px rgba(0, 0, 0, 0.6), 0 3px 6px rgba(0, 0, 0, 0.7)',
    da: '0 10px 20px rgba(0, 0, 0, 0.7), 0 6px 6px rgba(0, 0, 0, 0.8)',
    chaoda: '0 14px 28px rgba(0, 0, 0, 0.8), 0 10px 10px rgba(0, 0, 0, 0.9)',
  },
};

// 主题类型枚举
export const zhutileixing = {
  mingliang: 'mingliang',
  anhei: 'anhei',
};

// 默认主题
export const morenzuti = mingliangzhuti;

// 主题映射
export const zhutiyingshe = {
  [zhutileixing.mingliang]: mingliangzhuti,
  [zhutileixing.anhei]: anheizuti,
};

// 获取主题函数
export const huoquzhuti = (zhutimingcheng) => {
  return zhutiyingshe[zhutimingcheng] || morenzuti;
};
