import React from 'react';
import styled, { keyframes } from 'styled-components';

// 极光流动动画
const jiguangliudong1 = keyframes`
  0% {
    transform: translateX(-100%) translateY(-50%) rotate(0deg);
    opacity: 0;
  }
  50% {
    opacity: 0.3;
  }
  100% {
    transform: translateX(100vw) translateY(-30%) rotate(10deg);
    opacity: 0;
  }
`;

const jiguangliudong2 = keyframes`
  0% {
    transform: translateX(100%) translateY(50%) rotate(0deg);
    opacity: 0;
  }
  50% {
    opacity: 0.2;
  }
  100% {
    transform: translateX(-100vw) translateY(30%) rotate(-10deg);
    opacity: 0;
  }
`;

const jiguangliudong3 = keyframes`
  0% {
    transform: translateX(-50%) translateY(100%) rotate(45deg);
    opacity: 0;
  }
  50% {
    opacity: 0.25;
  }
  100% {
    transform: translateX(50%) translateY(-100vh) rotate(55deg);
    opacity: 0;
  }
`;

// 极光脉动动画
const jiguangmaidong = keyframes`
  0%, 100% {
    opacity: 0.1;
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(1.1);
  }
`;

// 极光背景容器
const Jiguang<PERSON> = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  pointer-events: none;
  z-index: -1;
  overflow: hidden;
  background: ${props => props.theme.yanse.beijing};
`;

// 极光层1 - 紫色
const Jiguangceng1 = styled.div`
  position: absolute;
  width: 200%;
  height: 100px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(138, 43, 226, 0.4) 20%, 
    rgba(75, 0, 130, 0.6) 50%, 
    rgba(138, 43, 226, 0.4) 80%, 
    transparent 100%
  );
  top: 20%;
  left: -50%;
  animation: ${jiguangliudong1} 15s infinite linear;
  filter: blur(2px);
`;

// 极光层2 - 青色
const Jiguangceng2 = styled.div`
  position: absolute;
  width: 150%;
  height: 80px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(0, 255, 127, 0.3) 25%, 
    rgba(0, 206, 209, 0.5) 50%, 
    rgba(0, 255, 127, 0.3) 75%, 
    transparent 100%
  );
  bottom: 30%;
  right: -25%;
  animation: ${jiguangliudong2} 20s infinite linear;
  animation-delay: -5s;
  filter: blur(1.5px);
`;

// 极光层3 - 蓝色
const Jiguangceng3 = styled.div`
  position: absolute;
  width: 120%;
  height: 60px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(30, 144, 255, 0.25) 30%, 
    rgba(65, 105, 225, 0.4) 50%, 
    rgba(30, 144, 255, 0.25) 70%, 
    transparent 100%
  );
  top: 60%;
  left: -10%;
  animation: ${jiguangliudong3} 25s infinite linear;
  animation-delay: -10s;
  filter: blur(1px);
`;

// 极光脉动层 - 粉色
const Jiguangmaidongceng = styled.div`
  position: absolute;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, 
    rgba(255, 20, 147, 0.2) 0%, 
    rgba(255, 105, 180, 0.1) 30%, 
    transparent 70%
  );
  top: 40%;
  right: 20%;
  animation: ${jiguangmaidong} 8s infinite ease-in-out;
  filter: blur(3px);
`;

// 星点效果
const Xingdian = styled.div`
  position: absolute;
  width: 2px;
  height: 2px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: ${jiguangmaidong} 3s infinite ease-in-out;
  
  &:nth-child(1) {
    top: 15%;
    left: 10%;
    animation-delay: 0s;
  }
  
  &:nth-child(2) {
    top: 25%;
    right: 15%;
    animation-delay: -1s;
  }
  
  &:nth-child(3) {
    bottom: 20%;
    left: 20%;
    animation-delay: -2s;
  }
  
  &:nth-child(4) {
    bottom: 35%;
    right: 25%;
    animation-delay: -0.5s;
  }
  
  &:nth-child(5) {
    top: 45%;
    left: 60%;
    animation-delay: -1.5s;
  }
`;

// 极光背景组件
export const Jiguangbeijing = ({ xianshi = true }) => {
  if (!xianshi) return null;

  return (
    <Jiguangrongqi>
      <Jiguangceng1 />
      <Jiguangceng2 />
      <Jiguangceng3 />
      <Jiguangmaidongceng />
      <Xingdian />
      <Xingdian />
      <Xingdian />
      <Xingdian />
      <Xingdian />
    </Jiguangrongqi>
  );
};

export default Jiguangbeijing;
