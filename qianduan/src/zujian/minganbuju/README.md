# 明暗布局组件 (minganbuju)

一个功能完整的 React 明暗主题切换组件，基于 styled-components 和 framer-motion 构建，提供优雅的动画过渡效果。

## ✨ 主要功能

- 🎨 **明亮/暗黑主题切换** - 内置两套精心设计的主题
- ✨ **优雅动画过渡** - 基于 framer-motion 的流畅动画效果
- 💾 **本地存储持久化** - 自动保存用户主题偏好
- ⌨️ **键盘快捷键** - 支持 Ctrl+Shift+T 快速切换
- 🔧 **高度可定制** - 支持自定义主题配置和动画效果
- 📱 **响应式设计** - 适配各种屏幕尺寸
- 🎯 **TypeScript 友好** - 完整的类型定义

## 🚀 快速开始

### 基本使用

```javascript
import React from 'react';
import { minganbuju } from './zujian/minganbuju';

function App() {
  return (
    <minganbuju>
      <div>
        <h1>你的应用内容</h1>
        <p>这里的内容会自动应用主题样式</p>
      </div>
    </minganbuju>
  );
}
```

### 带主题切换功能

```javascript
import React from 'react';
import { minganbuju, shiyongzhutiqiehuan } from './zujian/minganbuju';

function AppWithThemeToggle() {
  const { qiehuandaoxiayigezhuti, shifoianheizuti } = shiyongzhutiqiehuan();

  return (
    <minganbuju>
      <div>
        <h1>主题切换示例</h1>
        <p>当前模式: {shifoianheizuti ? '暗黑' : '明亮'}</p>
        <button onClick={qiehuandaoxiayigezhuti}>
          切换主题
        </button>
      </div>
    </minganbuju>
  );
}
```

## 📚 API 文档

### 主要组件

#### `minganbuju`
主布局组件，提供完整的主题功能。

**Props:**
- `chushipzhuti?: string` - 初始主题 (默认: 'mingliang')
- `donghualeixin?: string` - 动画类型 (默认: 'danrudanchu')
- `donghuasudu?: string` - 动画速度 (默认: 'zhongdeng')
- `qiyongquanjuyangshi?: boolean` - 是否启用全局样式 (默认: true)

#### `zhutitiqigong`
主题提供器，用于在已有 ThemeProvider 的应用中使用。

### 主要 Hooks

#### `shiyongzhutiqiehuan()`
主题切换 Hook，提供主题控制功能。

**返回值:**
```javascript
{
  qiehuanzhuti: (zhuti: string) => void,
  qiehuandaoxiayigezhuti: () => void,
  dangqianzhuti: string,
  shifoianheizuti: boolean
}
```

#### `shiyongzhutiqiehuanluoji()`
完整的主题切换逻辑 Hook，包含更多高级功能。

### 样式化组件

#### `zhutibiaomian`
主题表面组件，自动应用主题样式。

**Props:**
- `xuanfu?: boolean` - 悬浮效果
- `biankuang?: boolean` - 显示边框

#### `zhutiwenben`
主题文本组件。

**Props:**
- `leixing?: 'zhuyao' | 'ciyao' | 'jinzhi' | 'tishi'` - 文本类型
- `daxiao?: 'xiaoxiao' | 'xiao' | 'zhongdeng' | 'da' | 'dada' | 'chaoda'` - 字体大小
- `zhongliang?: 'xichang' | 'putong' | 'zhongdeng' | 'cuhei' | 'hei'` - 字体粗细

#### `zhutianniu`
主题按钮组件。

**Props:**
- `leixing?: 'zhuyao' | 'ciyao' | 'chenggong' | 'jinggao' | 'cuowu' | 'xinxi' | 'biankuang'` - 按钮类型
- `daxiao?: 'xiao' | 'zhongdeng' | 'da'` - 按钮大小
- `quankuan?: boolean` - 全宽显示

## 🎨 主题配置

### 内置主题

组件提供两套内置主题：

- **明亮主题** (`zhutileixing.MINGLIANG`)
- **暗黑主题** (`zhutileixing.ANHEI`)

### 自定义主题

```javascript
import { zhutiyingshe } from './zujian/minganbuju';

// 添加自定义主题
zhutiyingshe['zidingyi'] = {
  yanse: {
    beijing: '#f0f0f0',
    wenzi_zhuyao: '#333333',
    // ... 更多配置
  },
  // ... 其他主题属性
};
```

## 🎬 动画配置

### 动画类型

- `donghualeixin.DANRUDANCHU` - 淡入淡出
- `donghualeixin.HUADONG` - 滑动效果
- `donghualeixin.SUOFANG` - 缩放效果
- `donghualeixin.XUANZHUAN` - 旋转效果
- `donghualeixin.TANXING` - 弹性效果

### 动画速度

- `donghuasudu.KUAI` - 快速 (0.15s)
- `donghuasudu.ZHONGDENG` - 中等 (0.3s)
- `donghuasudu.MAN` - 慢速 (0.5s)

## ⌨️ 键盘快捷键

- `Ctrl+Shift+T` (Windows/Linux) 或 `Cmd+Shift+T` (Mac) - 切换主题

## 🔧 高级用法

### 监听系统主题变化

```javascript
import { shiyongzhutiqiehuanluoji } from './zujian/minganbuju';

function App() {
  const { qiehuanzidongmoshi, zidongqiehuan } = shiyongzhutiqiehuanluoji();

  // 启用自动跟随系统主题
  React.useEffect(() => {
    qiehuanzidongmoshi(true);
  }, []);

  return (
    <minganbuju>
      <p>自动跟随系统主题: {zidongqiehuan ? '已启用' : '已禁用'}</p>
    </minganbuju>
  );
}
```

### 自定义动画效果

```javascript
<minganbuju
  donghualeixin={donghualeixin.SUOFANG}
  donghuasudu={donghuasudu.MAN}
>
  {/* 你的内容 */}
</minganbuju>
```

## 📦 依赖

- React ^19.1.0
- styled-components ^6.1.19
- framer-motion ^12.23.12
- ahooks ^3.9.0

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
