// 明暗布局工具函数

import { zhu<PERSON><PERSON><PERSON>, zhu<PERSON><PERSON>she, morenzuti } from './zhutipeizhi.js';
import {
  bencunchujianjian,
  peizhi,
  morenzhi,
  jianche_liulanqi_huanjing,
  jianche_meiti_chaxun_z<PERSON>hi,
  huoqu_xitong_zhuti_pian<PERSON><PERSON>,
  jianche_shifo_anheizuti
} from './minganbuju_cuowuguanli.js';

// 打印明暗布局存储基础信息
function dayin_minganbuju_cunchu_jichuxinxi() {
  console.log('=== 明暗布局存储基础信息 ===');
  console.log('本地存储键名:', {
    主题存储键: bencunchujianjian.zhuti_cunchu_jian,
    自动切换存储键: bencunchujianjian.zidong_qiehuan_cunchu_jian
  });
  console.log('配置信息:', {
    存储前缀: peizhi.cunchu_qianzhui,
    数据格式: peizhi.shuju_geshi,
    调试模式: peizhi.tia<PERSON>_moshi,
    日志级别: peizhi.rizhi_jibei
  });
  console.log('默认值:', {
    默认主题: morenzhi.moren_zhu<PERSON>,
    默认有效期: morenzhi.moren_youxiaoqi,
    默认自动切换: morenzhi.moren_zidong_qiehuan
  });
  console.log('存储方式: localStorage');
  console.log('========================');
}

// 获取当前主题
export const huoqudangqianzhuti = () => {
  dayin_minganbuju_cunchu_jichuxinxi();
  console.log('主题读取操作 - 开始获取当前主题');

  if (!jianche_liulanqi_huanjing()) {
    console.log('主题读取操作 - 非浏览器环境，返回默认主题');
    return zhutileixing.mingliang;
  }

  const cunchu_zhuti = localStorage.getItem(bencunchujianjian.zhuti_cunchu_jian);
  console.log('主题读取操作 - 存储的主题:', cunchu_zhuti);

  const zuizhong_zhuti = cunchu_zhuti || zhutileixing.mingliang;
  console.log('主题读取操作 - 最终主题:', zuizhong_zhuti);

  return zuizhong_zhuti;
};

// 检查系统主题偏好
export const jianchaxitongzhuti = () => {
  const xitong_zhuti = huoqu_xitong_zhuti_pianhaoo();
  return zhutileixing[xitong_zhuti] || zhutileixing.mingliang;
};

// 设置页面背景颜色
export const shezhibeijingyanse = (zhutimingcheng) => {
  if (typeof document === 'undefined') {
    return;
  }
  
  const zhuti = zhutiyingshe[zhutimingcheng] || morenzuti;
  document.body.style.backgroundColor = zhuti.yanse.beijing;
  document.body.style.color = zhuti.yanse.wenzi_zhuyao;
};

// 获取主题颜色
export const huoquzhutiyanse = (zhutimingcheng, yansemingcheng) => {
  const zhuti = zhutiyingshe[zhutimingcheng] || morenzuti;
  
  // 支持嵌套属性访问，如 'yanse.beijing'
  const shuxinglujing = yansemingcheng.split('.');
  let jieguo = zhuti;
  
  for (const shuxing of shuxinglujing) {
    if (jieguo && typeof jieguo === 'object' && shuxing in jieguo) {
      jieguo = jieguo[shuxing];
    } else {
      return null;
    }
  }
  
  return jieguo;
};

// 获取对比颜色（用于确保文字可读性）
export const huoquduibiyanse = (zhutimingcheng) => {
  const zhuti = zhutiyingshe[zhutimingcheng] || morenzuti;
  return jianche_shifo_anheizuti(zhutimingcheng)
    ? zhuti.yanse.wenzi_zhuyao
    : zhuti.yanse.wenzi_zhuyao;
};

// 判断颜色是否为深色
export const panduanshenseyanse = (yanse) => {
  if (!yanse || typeof yanse !== 'string') {
    return false;
  }
  
  // 移除 # 符号
  const hex = yanse.replace('#', '');
  
  // 转换为 RGB
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);
  
  // 计算亮度
  const liangdu = (r * 299 + g * 587 + b * 114) / 1000;
  
  return liangdu < 128;
};

// 生成主题 CSS 变量
export const shengchengzhuti_css_bianliang = (zhutimingcheng) => {
  const zhuti = zhutiyingshe[zhutimingcheng] || morenzuti;
  const cssbianliang = {};
  
  // 递归处理主题对象
  const chulishuxing = (obj, qianzhui = '') => {
    Object.keys(obj).forEach(jian => {
      const zhi = obj[jian];
      const bianliangjian = qianzhui ? `${qianzhui}-${jian}` : jian;
      
      if (typeof zhi === 'object' && zhi !== null) {
        chulishuxing(zhi, bianliangjian);
      } else {
        cssbianliang[`--${bianliangjian}`] = zhi;
      }
    });
  };
  
  chulishuxing(zhuti);
  return cssbianliang;
};

// 应用主题到 CSS 变量
export const yingyongzhutidao_css_bianliang = (zhutimingcheng) => {
  if (typeof document === 'undefined') {
    return;
  }
  
  const cssbianliang = shengchengzhuti_css_bianliang(zhutimingcheng);
  const root = document.documentElement;
  
  Object.keys(cssbianliang).forEach(bianliang => {
    root.style.setProperty(bianliang, cssbianliang[bianliang]);
  });
};

// 监听系统主题变化
export const jianchaxitongzhuti_bianhua = (huidiaohansu) => {
  if (!jianche_meiti_chaxun_zhichi()) {
    return () => {};
  }

  const meiti_chaxun = window.matchMedia(peizhi.meiti_chaxun_anheimoshi);

  const chulibianhua = (event) => {
    const xinzhuti = event.matches ? zhutileixing.anhei : zhutileixing.mingliang;
    huidiaohansu(xinzhuti);
  };

  meiti_chaxun.addEventListener('change', chulibianhua);

  // 返回清理函数
  return () => {
    meiti_chaxun.removeEventListener('change', chulibianhua);
  };
};

// 获取主题元数据
export const huoquzhuti_yuanshuju = (zhutimingcheng) => {
  const zhuti = zhutiyingshe[zhutimingcheng] || morenzuti;
  
  return {
    mingcheng: zhutimingcheng,
    shifoianheizuti: jianche_shifo_anheizuti(zhutimingcheng),
    zhuyaoyanse: zhuti.yanse.zhuyao,
    beijingyanse: zhuti.yanse.beijing,
    wenziyanse: zhuti.yanse.wenzi_zhuyao,
    zhuti: zhuti,
  };
};
