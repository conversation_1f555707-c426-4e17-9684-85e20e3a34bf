import React, { createContext, useContext } from 'react';
import { ThemeProvider } from 'styled-components';
import { useLocalStorageState } from 'ahooks';
import {
  zhutileixing,
  zhutiyingshe,
  morenzuti,
  mingliangzhuti,
  anheizuti
} from './zhutipeizhi.js';
import { bencunchujianjian, cuowuxiaoxi, jianche_shifo_anheizuti } from './minganbuju_cuowuguanli.js';

// 创建主题上下文
const zhutishangxiaweng = createContext();

// 主题提供器组件
export const Zhutitiqigong = ({ children, chushipzhuti = zhutileixing.mingliang }) => {
  // 使用 ahooks 的 useLocalStorageState 管理主题持久化
  const [dangqian<PERSON>hu<PERSON>, shezhi_dangqianzhuti] = useLocalStorageState(
    bencunchujianjian.zhuti_cunchu_jian,
    {
      defaultValue: chushipzhu<PERSON>,
    }
  );

  // 获取当前主题对象，并添加主题名称
  const zhutidixiang = {
    ...(zhu<PERSON><PERSON><PERSON>[dangqi<PERSON><PERSON><PERSON><PERSON>] || morenzuti),
    mingcheng: dangqi<PERSON><PERSON><PERSON><PERSON>
  };

  // 切换主题函数（移除动画逻辑，实现即时切换）
  const qiehuanzhuti = (xinzhuti) => {
    console.log('🎨 [主题系统] 尝试切换主题', {
      cong: dangqianzhuti,
      dao: xinzhuti,
      shifo_xuyao_qiehuan: xinzhuti && xinzhuti !== dangqianzhuti,
      shijian: new Date().toISOString()
    });

    if (xinzhuti && xinzhuti !== dangqianzhuti) {
      shezhi_dangqianzhuti(xinzhuti);
      console.log('🎨 [主题系统] 主题切换完成', {
        xinzhuti,
        shijian: new Date().toISOString()
      });
    }
  };

  // 切换到下一个主题
  const qiehuandaoxiayigezhuti = () => {
    const zhutiliebiao = Object.values(zhutileixing);
    const dangqiansuoyin = zhutiliebiao.indexOf(dangqianzhuti);
    const xiayigesuoyin = (dangqiansuoyin + 1) % zhutiliebiao.length;
    qiehuanzhuti(zhutiliebiao[xiayigesuoyin]);
  };

  // 检查是否为暗黑主题
  const shifoianheizuti = jianche_shifo_anheizuti(dangqianzhuti);

  // 上下文值
  const shangxiawengzhi = {
    dangqianzhuti,
    zhutidixiang,
    qiehuanzhuti,
    qiehuandaoxiayigezhuti,
    shifoianheizuti,
    zhutileixing,
  };

  return (
    <zhutishangxiaweng.Provider value={shangxiawengzhi}>
      <ThemeProvider theme={zhutidixiang}>
        <div
          style={{
            width: '100%',
            height: '100%',
            backgroundColor: zhutidixiang.yanse.beijing,
            color: zhutidixiang.yanse.wenzi_zhuyao,
          }}
        >
          {children}
        </div>
      </ThemeProvider>
    </zhutishangxiaweng.Provider>
  );
};

// 自定义 Hook 用于使用主题上下文
export const useShiyongzhuti = () => {
  const shangxiaweng = useContext(zhutishangxiaweng);
  
  if (!shangxiaweng) {
    throw new Error(cuowuxiaoxi.zhuti_weixiaohua);
  }
  
  return shangxiaweng;
};

// 高阶组件：为组件提供主题功能
export const daizhutizujian = (Zujian) => {
  return React.forwardRef((props, ref) => (
    <Zhutitiqigong>
      <Zujian {...props} ref={ref} />
    </Zhutitiqigong>
  ));
};

// 主题切换 Hook
export const useShiyongzhutiqiehuan = () => {
  const {
    qiehuanzhuti,
    qiehuandaoxiayigezhuti,
    dangqianzhuti,
    shifoianheizuti
  } = useShiyongzhuti();

  return {
    qiehuanzhuti,
    qiehuandaoxiayigezhuti,
    dangqianzhuti,
    shifoianheizuti,
    zhutileixing, // 添加主题类型常量
  };
};
