import { useEffect, useCallback } from 'react';
import { useLocalStorageState, useEventListener } from 'ahooks';
import { zhutileixing } from './zhutipeizhi.js';
import {
  bencunchujianjian,
  kuaijiejian,
  peizhi,
  jianche_meiti_chaxun_zhichi,
  huoqu_xitong_zhuti_pian<PERSON>o,
  jianche_shifo_anheizuti,
  jianche_shifo_mingliangzhuti
} from './minganbuju_cuowuguanli.js';

// 主题切换逻辑 Hook
export const useShiyongzhutiqiehuanluoji = (chushipzhuti = zhutileixing.mingliang) => {
  // 主题状态管理
  const [dangqianzhuti, shezhi_dangqianzhuti] = useLocalStorageState(
    bencunchujianjian.zhuti_cunchu_jian,
    {
      defaultValue: chushipzhuti,
    }
  );

  // 自动切换设置
  const [zidong<PERSON><PERSON><PERSON>, shezhi_zidongqiehuan] = useLocalStorageState(
    bencunchujianjian.zidong_qiehuan_cunchu_jian,
    {
      defaultValue: false,
    }
  );

  // 切换主题函数（移除动画逻辑，实现即时切换）
  const qiehuanzhuti = useCallback((xinzhuti) => {
    if (xinzhuti && xinzhuti !== dangqianzhuti) {
      shezhi_dangqianzhuti(xinzhuti);
    }
  }, [dangqianzhuti, shezhi_dangqianzhuti]);

  // 切换到明亮主题
  const qiehuandaomingliangzhuti = useCallback(() => {
    qiehuanzhuti(zhutileixing.mingliang);
  }, [qiehuanzhuti]);

  // 切换到暗黑主题
  const qiehuandaoanheizuti = useCallback(() => {
    qiehuanzhuti(zhutileixing.anhei);
  }, [qiehuanzhuti]);

  // 切换到下一个主题
  const qiehuandaoxiayigezhuti = useCallback(() => {
    const zhutiliebiao = Object.values(zhutileixing);
    const dangqiansuoyin = zhutiliebiao.indexOf(dangqianzhuti);
    const xiayigesuoyin = (dangqiansuoyin + 1) % zhutiliebiao.length;
    qiehuanzhuti(zhutiliebiao[xiayigesuoyin]);
  }, [dangqianzhuti, qiehuanzhuti]);

  // 检查是否为暗黑主题
  const shifoianheizuti = jianche_shifo_anheizuti(dangqianzhuti);

  // 检查是否为明亮主题
  const shifoimingliangzhuti = jianche_shifo_mingliangzhuti(dangqianzhuti);

  // 根据系统主题自动切换
  const genjuxitongzhutiqiehuan = useCallback(() => {
    if (jianche_meiti_chaxun_zhichi()) {
      const xitong_zhuti = huoqu_xitong_zhuti_pianhaoo();
      const mubiaozuti = zhutileixing[xitong_zhuti] || zhutileixing.mingliang;

      if (mubiaozuti !== dangqianzhuti) {
        qiehuanzhuti(mubiaozuti);
      }
    }
  }, [dangqianzhuti, qiehuanzhuti]);

  // 监听系统主题变化
  useEventListener(
    'change',
    genjuxitongzhutiqiehuan,
    {
      target: jianche_meiti_chaxun_zhichi()
        ? window.matchMedia(peizhi.meiti_chaxun_anheimoshi)
        : null,
    }
  );

  // 启用/禁用自动切换
  const qiehuanzidongmoshi = useCallback((qiyong) => {
    shezhi_zidongqiehuan(qiyong);
    
    if (qiyong) {
      genjuxitongzhutiqiehuan();
    }
  }, [shezhi_zidongqiehuan, genjuxitongzhutiqiehuan]);

  // 初始化时检查是否需要自动切换
  useEffect(() => {
    if (zidongqiehuan) {
      genjuxitongzhutiqiehuan();
    }
  }, [zidongqiehuan, genjuxitongzhutiqiehuan]);

  // 键盘快捷键支持
  useEventListener('keydown', (event) => {
    // Ctrl/Cmd + Shift + T 切换主题
    if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === kuaijiejian.zhuti_qiehuan) {
      event.preventDefault();
      qiehuandaoxiayigezhuti();
    }
  });

  return {
    // 状态
    dangqianzhuti,
    shifoianheizuti,
    shifoimingliangzhuti,
    zidongqiehuan,

    // 切换函数
    qiehuanzhuti,
    qiehuandaomingliangzhuti,
    qiehuandaoanheizuti,
    qiehuandaoxiayigezhuti,
    genjuxitongzhutiqiehuan,
    qiehuanzidongmoshi,

    // 主题类型
    zhutileixing,
  };
};

// 主题状态 Hook（只读）
export const useShiyongzhutizhuangtai = () => {
  const [dangqianzhuti] = useLocalStorageState(
    bencunchujianjian.zhuti_cunchu_jian,
    {
      defaultValue: zhutileixing.mingliang,
    }
  );

  const [zidongqiehuan] = useLocalStorageState(
    bencunchujianjian.zidong_qiehuan_cunchu_jian,
    {
      defaultValue: false,
    }
  );

  return {
    dangqianzhuti,
    shifoianheizuti: jianche_shifo_anheizuti(dangqianzhuti),
    shifoimingliangzhuti: jianche_shifo_mingliangzhuti(dangqianzhuti),
    zidongqiehuan,
    zhutileixing,
  };
};
