/**
 * 路由重定向管理类
 * 负责处理前端路由到后端接口的重定向逻辑
 */

class luyou_chongdingxiang_guanli {
  constructor() {
    this.chongdingxiang_guize = new Map();
    this.jianting_qiyong = false;
    this.yuanshi_pushstate = null;
    this.yuanshi_replacestate = null;
    this.yuanshi_popstate_jianting = null;
    
    // 初始化默认重定向规则
    this.chushihua_moren_guize();
  }

  /**
   * 初始化默认重定向规则
   */
  chushihua_moren_guize() {
    // 添加 /ziyuan 到 /ziyuanhuoqu 的重定向规则
    this.tianjia_chongdingxiang_guize('/ziyuan', '/ziyuanhuoqu');
  }

  /**
   * 添加重定向规则
   * @param {string} yuanshi_lujing - 原始路径前缀
   * @param {string} mubiao_lujing - 目标路径前缀
   */
  tianjia_chongdingxiang_guize(yuanshi_lujing, mubiao_lujing) {
    this.chongdingxiang_guize.set(yuanshi_lujing, mubiao_lujing);
  }

  /**
   * 移除重定向规则
   * @param {string} yuanshi_lujing - 原始路径前缀
   */
  yichu_chongdingxiang_guize(yuanshi_lujing) {
    if (this.chongdingxiang_guize.has(yuanshi_lujing)) {
      this.chongdingxiang_guize.delete(yuanshi_lujing);
    }
  }

  /**
   * 检查路径是否需要重定向
   * @param {string} lujing - 当前路径
   * @returns {string|null} 重定向后的路径，如果不需要重定向则返回null
   */
  jiancha_chongdingxiang(lujing) {
    for (const [yuanshi_lujing, mubiao_lujing] of this.chongdingxiang_guize) {
      if (lujing.startsWith(yuanshi_lujing)) {
        // 替换路径前缀
        const chongdingxiang_lujing = lujing.replace(yuanshi_lujing, mubiao_lujing);
        return chongdingxiang_lujing;
      }
    }
    return null;
  }

  /**
   * 执行重定向
   * @param {string} mubiao_lujing - 目标路径
   */
  zhixing_chongdingxiang(mubiao_lujing) {
    try {
      // 构建完整的后端URL
      const houduan_url = `http://127.0.0.1:8098/jiekou${mubiao_lujing}`;
      
      // 直接跳转到后端URL
      window.location.href = houduan_url;
    } catch (cuowu) {
    }
  }

  /**
   * 处理路径变化
   * @param {string} xinlujing - 新路径
   */
  chuli_lujing_bianhua(xinlujing) {
    const chongdingxiang_lujing = this.jiancha_chongdingxiang(xinlujing);
    if (chongdingxiang_lujing) {
      // 阻止默认的路由行为，执行重定向
      this.zhixing_chongdingxiang(chongdingxiang_lujing);
      return true; // 表示已处理重定向
    }
    return false; // 表示无需重定向
  }

  /**
   * 启用路由监听
   */
  qiyong_luyou_jianting() {
    if (this.jianting_qiyong) {
      return;
    }

    // 保存原始方法
    this.yuanshi_pushstate = window.history.pushState;
    this.yuanshi_replacestate = window.history.replaceState;

    // 重写 pushState 方法
    window.history.pushState = (state, title, url) => {
      if (url && typeof url === 'string') {
        const lujing = url.startsWith('/') ? url : new URL(url, window.location.origin).pathname;
        if (this.chuli_lujing_bianhua(lujing)) {
          return; // 如果执行了重定向，不继续原始操作
        }
      }
      this.yuanshi_pushstate.call(window.history, state, title, url);
    };

    // 重写 replaceState 方法
    window.history.replaceState = (state, title, url) => {
      if (url && typeof url === 'string') {
        const lujing = url.startsWith('/') ? url : new URL(url, window.location.origin).pathname;
        if (this.chuli_lujing_bianhua(lujing)) {
          return; // 如果执行了重定向，不继续原始操作
        }
      }
      this.yuanshi_replacestate.call(window.history, state, title, url);
    };

    // 监听 popstate 事件（浏览器前进后退）
    this.yuanshi_popstate_jianting = (event) => {
      const dangqian_lujing = window.location.pathname;
      this.chuli_lujing_bianhua(dangqian_lujing);
    };
    window.addEventListener('popstate', this.yuanshi_popstate_jianting);

    // 监听直接URL访问
    this.jianting_zhijie_fangwen();

    this.jianting_qiyong = true;
  }

  /**
   * 监听直接URL访问
   */
  jianting_zhijie_fangwen() {
    // 检查当前页面是否需要重定向
    const dangqian_lujing = window.location.pathname;
    this.chuli_lujing_bianhua(dangqian_lujing);
  }

  /**
   * 禁用路由监听
   */
  jinyong_luyou_jianting() {
    if (!this.jianting_qiyong) {
      return;
    }

    // 恢复原始方法
    if (this.yuanshi_pushstate) {
      window.history.pushState = this.yuanshi_pushstate;
      this.yuanshi_pushstate = null;
    }

    if (this.yuanshi_replacestate) {
      window.history.replaceState = this.yuanshi_replacestate;
      this.yuanshi_replacestate = null;
    }

    // 移除事件监听器
    if (this.yuanshi_popstate_jianting) {
      window.removeEventListener('popstate', this.yuanshi_popstate_jianting);
      this.yuanshi_popstate_jianting = null;
    }

    this.jianting_qiyong = false;
  }

  /**
   * 获取所有重定向规则
   * @returns {Map} 重定向规则映射
   */
  huoqu_suoyou_guize() {
    return new Map(this.chongdingxiang_guize);
  }

  /**
   * 清空所有重定向规则
   */
  qingkong_suoyou_guize() {
    this.chongdingxiang_guize.clear();
  }

  /**
   * 销毁管理器
   */
  xiaohui() {
    this.jinyong_luyou_jianting();
    this.qingkong_suoyou_guize();
  }
}

// 创建全局单例实例
let quanju_chongdingxiang_guanli = null;

/**
 * 获取全局路由重定向管理器实例
 * @returns {luyou_chongdingxiang_guanli} 管理器实例
 */
export function huoqu_chongdingxiang_guanli() {
  if (!quanju_chongdingxiang_guanli) {
    quanju_chongdingxiang_guanli = new luyou_chongdingxiang_guanli();
  }
  return quanju_chongdingxiang_guanli;
}

/**
 * 初始化路由重定向功能
 */
export function chushihua_luyou_chongdingxiang() {
  const guanli = huoqu_chongdingxiang_guanli();
  guanli.qiyong_luyou_jianting();
  return guanli;
}

/**
 * 销毁路由重定向功能
 */
export function xiaohui_luyou_chongdingxiang() {
  if (quanju_chongdingxiang_guanli) {
    quanju_chongdingxiang_guanli.xiaohui();
    quanju_chongdingxiang_guanli = null;
  }
}

export default luyou_chongdingxiang_guanli;
