import cuowurizhi from './chushihua_cuowurizhi.js';

/**
 * 网站动态应用模块
 */
function shezhi_wangzhan_biaoti(wangzhan_mingcheng) {
  try {
    if (!wangzhan_mingcheng || typeof wangzhan_mingcheng !== 'string') {
      return false;
    }
    document.title = wangzhan_mingcheng;
    return true;
  } catch (cuowu) {
    console.error(cuowurizhi.shezhi_wangzhan_biaoti_shibai, cuowu);
    return false;
  }
}

function shezhi_wangzhan_tubiao(tubiao_lianjie) {
  try {
    if (!tubiao_lianjie || typeof tubiao_lianjie !== 'string') {
      return false;
    }

    // 移除所有现有的favicon链接
    const xianyou_favicon = document.querySelectorAll('link[rel*="icon"]');
    xianyou_favicon.forEach(yuansu => yuansu.remove());

    // 根据图片格式确定MIME类型
    let mime_leixing = 'image/x-icon';
    const xiaoxie_lianjie = tubiao_lianjie.toLowerCase();
    if (xiaoxie_lianjie.includes('.png')) {
      mime_leixing = 'image/png';
    } else if (xiaoxie_lianjie.includes('.jpg') || xiaoxie_lianjie.includes('.jpeg')) {
      mime_leixing = 'image/jpeg';
    } else if (xiaoxie_lianjie.includes('.gif')) {
      mime_leixing = 'image/gif';
    } else if (xiaoxie_lianjie.includes('.svg')) {
      mime_leixing = 'image/svg+xml';
    }

    // 创建新的favicon链接
    const favicon_yuansu = document.createElement('link');
    favicon_yuansu.rel = 'icon';
    favicon_yuansu.type = mime_leixing;
    favicon_yuansu.href = tubiao_lianjie + '?t=' + Date.now(); // 添加时间戳防止缓存
    document.head.appendChild(favicon_yuansu);

    // 同时创建shortcut icon链接以兼容旧浏览器
    const shortcut_yuansu = document.createElement('link');
    shortcut_yuansu.rel = 'shortcut icon';
    shortcut_yuansu.type = mime_leixing;
    shortcut_yuansu.href = tubiao_lianjie + '?t=' + Date.now();
    document.head.appendChild(shortcut_yuansu);

    console.log('网站图标设置成功:', tubiao_lianjie);
    return true;
  } catch (cuowu) {
    console.error(cuowurizhi.shezhi_wangzhan_tubiao_shibai, cuowu);
    return false;
  }
}

function yingyong_wangzhan_jichuxinxi(wangzhan_xinxi) {
  const yingyong_jieguo = {
    chenggong_shuliang: 0,
    shibai_shuliang: 0,
    xiangxi_jieguo: {}
  };
  try {
    if (!wangzhan_xinxi || typeof wangzhan_xinxi !== 'object') {
      console.error(cuowurizhi.wangzhan_jichuxinxi_shuju_wuxiao);
      return yingyong_jieguo;
    }
    if (wangzhan_xinxi.wangzhan_mingcheng) {
      const biaoti_jieguo = shezhi_wangzhan_biaoti(wangzhan_xinxi.wangzhan_mingcheng);
      yingyong_jieguo.xiangxi_jieguo.biaoti = biaoti_jieguo;
      if (biaoti_jieguo) {
        yingyong_jieguo.chenggong_shuliang++;
      } else {
        yingyong_jieguo.shibai_shuliang++;
      }
    }
    if (wangzhan_xinxi.wangzhan_tubiao_lianjie) {
      const tubiao_jieguo = shezhi_wangzhan_tubiao(wangzhan_xinxi.wangzhan_tubiao_lianjie);
      yingyong_jieguo.xiangxi_jieguo.tubiao = tubiao_jieguo;
      if (tubiao_jieguo) {
        yingyong_jieguo.chenggong_shuliang++;
      } else {
        yingyong_jieguo.shibai_shuliang++;
      }
    }
    return yingyong_jieguo;
  } catch (cuowu) {
    console.error(cuowurizhi.dongtai_yingyong_wangzhan_jichuxinxi_shibai, cuowu);
    yingyong_jieguo.shibai_shuliang++;
    return yingyong_jieguo;
  }
}

function kuaisu_yingyong_mingcheng_tubiao(wangzhan_xinxi) {
  try {
    if (!wangzhan_xinxi || typeof wangzhan_xinxi !== 'object') {
      console.error(cuowurizhi.wangzhan_jichuxinxi_shuju_wuxiao);
      return false;
    }

    console.log('🎯 [动态应用] 开始应用网站基础信息:', wangzhan_xinxi);
    let chenggong_biaozhi = true;

    if (wangzhan_xinxi.wangzhan_mingcheng) {
      console.log('🎯 [动态应用] 设置网站标题:', wangzhan_xinxi.wangzhan_mingcheng);
      if (!shezhi_wangzhan_biaoti(wangzhan_xinxi.wangzhan_mingcheng)) {
        chenggong_biaozhi = false;
      }
    }

    if (wangzhan_xinxi.wangzhan_tubiao_lianjie) {
      console.log('🎯 [动态应用] 设置网站图标:', wangzhan_xinxi.wangzhan_tubiao_lianjie);
      if (!shezhi_wangzhan_tubiao(wangzhan_xinxi.wangzhan_tubiao_lianjie)) {
        chenggong_biaozhi = false;
      }
    }

    console.log('🎯 [动态应用] 应用结果:', chenggong_biaozhi ? '成功' : '失败');
    return chenggong_biaozhi;
  } catch (cuowu) {
    console.error(cuowurizhi.kuaisu_yingyong_mingcheng_tubiao_shibai, cuowu);
    return false;
  }
}

export {
  shezhi_wangzhan_biaoti,
  shezhi_wangzhan_tubiao,
  yingyong_wangzhan_jichuxinxi,
  kuaisu_yingyong_mingcheng_tubiao
};
