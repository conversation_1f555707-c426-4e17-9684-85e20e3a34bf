import React from 'react';
import { useMemoizedFn } from 'ahooks';

/**
 * 通用设备适配工具模块
 * 提供设备类型检测、响应式断点处理、样式生成等功能
 * 可被项目中的任何组件使用
 */

// 设备类型常量
export const shebeileixing = {
  shouji: 'shouji',     // 手机
  pingban: 'pingban',   // 平板
  zhuomian: 'zhuomian'  // 桌面
};

// 响应式断点配置
export const duandian = {
  ji_xiao_shouji: 360,  // 极小手机（如iPhone SE）
  xiao_shouji: 400,     // 小手机
  shouji_max: 480,      // 手机最大宽度
  pingban_min: 481,     // 平板最小宽度
  pingban_max: 1366,    // 平板最大宽度（支持iPad Pro等大屏平板）
  zhuomian_min: 1367,   // 桌面最小宽度

  // 桌面端精细断点（用于菜单适配）
  zhuomian_xiao: 1367,  // 小桌面（菜单紧凑）
  zhuomian_zhongdeng: 1600, // 中等桌面（菜单正常）
  zhuomian_da: 1920     // 大桌面（菜单宽松）
};

/**
 * 高级设备特征检测
 * @returns {Object} 设备特征信息
 */
const huoqu_shebeitexing = () => {
  const kuandu = window.innerWidth;
  const gaodu = window.innerHeight;
  const shifoichudian = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  const yonghudaili = navigator.userAgent.toLowerCase();
  const pixelRatio = window.devicePixelRatio || 1;

  // 检测具体设备类型
  const shifoiipad = /ipad/i.test(yonghudaili) ||
    (navigator.userAgentData?.platform === 'macOS' && navigator.maxTouchPoints > 1);
  const shifoiandroidpingban = /android/i.test(yonghudaili) && !/mobile/i.test(yonghudaili);
  const shifoiphone = /iphone/i.test(yonghudaili);
  const shifoiandroidshouji = /android/i.test(yonghudaili) && /mobile/i.test(yonghudaili);

  // 检测是否支持hover（鼠标悬停）
  const zhichi_hover = window.matchMedia('(hover: hover)').matches;
  const zhichi_pointer = window.matchMedia('(pointer: fine)').matches;

  // 计算屏幕比例
  const kuangao_bili = kuandu / gaodu;

  return {
    kuandu,
    gaodu,
    shifoichudian,
    shifoiipad,
    shifoiandroidpingban,
    shifoiphone,
    shifoiandroidshouji,
    zhichi_hover,
    zhichi_pointer,
    kuangao_bili,
    pixelRatio,
    yonghudaili
  };
};

/**
 * 设备类型检测 Hook
 * @returns {Function} 返回设备类型检测函数
 */
export const useShebeileixingJiance = () => {
  const huoqu_shebeileixing = useMemoizedFn(() => {
    const texing = huoqu_shebeitexing();

    // 优先级1: 明确的设备标识
    if (texing.shifoiipad) {
      return shebeileixing.pingban;
    }

    if (texing.shifoiphone || texing.shifoiandroidshouji) {
      return shebeileixing.shouji;
    }

    if (texing.shifoiandroidpingban) {
      return shebeileixing.pingban;
    }

    // 优先级2: 触摸设备 + 屏幕尺寸判断
    if (texing.shifoichudian) {
      // 触摸设备的尺寸判断
      if (texing.kuandu <= duandian.shouji_max) {
        return shebeileixing.shouji;
      } else if (texing.kuandu <= duandian.pingban_max) {
        return shebeileixing.pingban;
      } else {
        // 大屏触摸设备，可能是触摸屏笔记本，但如果不支持精确指针，仍视为平板
        return !texing.zhichi_pointer ? shebeileixing.pingban : shebeileixing.zhuomian;
      }
    }

    // 优先级3: 非触摸设备的尺寸判断
    if (texing.kuandu <= duandian.shouji_max) {
      return shebeileixing.shouji; // 小屏幕设备
    } else if (texing.kuandu <= duandian.pingban_max && !texing.zhichi_hover) {
      return shebeileixing.pingban; // 中等屏幕且不支持hover
    } else {
      return shebeileixing.zhuomian; // 桌面设备
    }
  });

  // 调试信息输出（仅在开发环境）
  const shuchu_tiaoshi_xinxi = useMemoizedFn(() => {
    if (process.env.NODE_ENV === 'development') {
      const texing = huoqu_shebeitexing();
      const shebei = huoqu_shebeileixing();

      console.group('🔍 设备检测调试信息');
      console.log('📱 设备类型:', shebei);
      console.log('📏 屏幕尺寸:', `${texing.kuandu}x${texing.gaodu}`);
      console.log('👆 触摸支持:', texing.shifoichudian);
      console.log('🖱️ Hover支持:', texing.zhichi_hover);
      console.log('🎯 精确指针:', texing.zhichi_pointer);
      console.log('📱 iPad检测:', texing.shifoiipad);
      console.log('🤖 Android平板:', texing.shifoiandroidpingban);
      console.log('📞 iPhone检测:', texing.shifoiphone);
      console.log('📱 Android手机:', texing.shifoiandroidshouji);
      console.log('🔍 User Agent:', texing.yonghudaili);
      console.log('📐 宽高比例:', texing.kuangao_bili.toFixed(2));
      console.log('🔍 像素比例:', texing.pixelRatio);
      console.groupEnd();
    }
  });

  // 获取屏幕尺寸分类
  const huoqu_pingmu_fenlei = useMemoizedFn(() => {
    const kuandu = window.innerWidth;

    if (kuandu <= duandian.ji_xiao_shouji) {
      return 'ji_xiao'; // 极小屏幕
    } else if (kuandu <= duandian.xiao_shouji) {
      return 'xiao'; // 小屏幕
    } else if (kuandu <= duandian.shouji_max) {
      return 'zhongdeng'; // 中等屏幕
    } else if (kuandu <= duandian.pingban_max) {
      return 'da'; // 大屏幕
    } else {
      return 'chao_da'; // 超大屏幕
    }
  });

  return { huoqu_shebeileixing, shuchu_tiaoshi_xinxi, huoqu_pingmu_fenlei };
};

/**
 * 移动端检测 Hook
 * @returns {Function} 返回移动端检测函数
 */
export const useYidongduanJiance = () => {
  const { huoqu_shebeileixing } = useShebeileixingJiance();
  
  const jiance_yidongduan = useMemoizedFn(() => {
    return huoqu_shebeileixing() === shebeileixing.shouji;
  });

  return { jiance_yidongduan };
};

/**
 * 平板检测 Hook
 * @returns {Function} 返回平板检测函数
 */
export const usePingbanJiance = () => {
  const { huoqu_shebeileixing } = useShebeileixingJiance();
  
  const jiance_pingban = useMemoizedFn(() => {
    return huoqu_shebeileixing() === shebeileixing.pingban;
  });

  return { jiance_pingban };
};

/**
 * 桌面端检测 Hook
 * @returns {Function} 返回桌面端检测函数
 */
export const useZhuomianduanJiance = () => {
  const { huoqu_shebeileixing } = useShebeileixingJiance();
  
  const jiance_zhuomianduan = useMemoizedFn(() => {
    return huoqu_shebeileixing() === shebeileixing.zhuomian;
  });

  return { jiance_zhuomianduan };
};

/**
 * 智能侧边栏控制Hook
 * @returns {Function} 返回侧边栏关闭判断函数
 */
export const useZhengming_cebiankuang_guanbi = () => {
  const { huoqu_shebeileixing } = useShebeileixingJiance();

  const zhengming_cebiankuang_guanbi = useMemoizedFn((responsive, cebiankuangkaiqii) => {
    const shebei = huoqu_shebeileixing();

    // 更严格的自动关闭条件：
    // 1. 必须是桌面设备
    // 2. 屏幕宽度必须大于1400px（xl断点）
    // 3. 侧边栏当前是打开状态
    // 这样可以避免大屏平板（如iPad Pro 1024x1366）被误关闭
    return responsive?.xl && shebei === shebeileixing.zhuomian && cebiankuangkaiqii;
  });

  return { zhengming_cebiankuang_guanbi };
};

/**
 * 生成设备特定的CSS媒体查询
 */
export const meiti_chaxun = {
  // 极小手机端
  ji_xiao_shouji: `@media (max-width: ${duandian.ji_xiao_shouji}px)`,

  // 小手机端
  xiao_shouji: `@media (min-width: ${duandian.ji_xiao_shouji + 1}px) and (max-width: ${duandian.xiao_shouji}px)`,

  // 中等手机端
  zhongdeng_shouji: `@media (min-width: ${duandian.xiao_shouji + 1}px) and (max-width: ${duandian.shouji_max}px)`,

  // 所有手机端
  shouji: `@media (max-width: ${duandian.shouji_max}px)`,

  // 平板端（触摸设备，支持大屏平板）
  pingban: `@media (min-width: ${duandian.pingban_min}px) and (max-width: ${duandian.pingban_max}px) and (hover: none) and (pointer: coarse)`,

  // 小屏幕桌面端
  xiaoping_zhuomian: `@media (min-width: ${duandian.pingban_min}px) and (max-width: 768px) and (hover: hover) and (pointer: fine)`,

  // 中等屏幕桌面端（避免与大屏平板冲突）
  zhongdeng_zhuomian: `@media (min-width: 769px) and (max-width: 1366px) and (hover: hover) and (pointer: fine)`,

  // 小桌面端（菜单紧凑）
  xiao_zhuomian: `@media (min-width: ${duandian.zhuomian_xiao}px) and (max-width: ${duandian.zhuomian_zhongdeng - 1}px) and (hover: hover) and (pointer: fine)`,

  // 中等桌面端（菜单正常）
  zhongdeng_zhuomian_new: `@media (min-width: ${duandian.zhuomian_zhongdeng}px) and (max-width: ${duandian.zhuomian_da - 1}px) and (hover: hover) and (pointer: fine)`,

  // 大桌面端（菜单宽松）
  da_zhuomian: `@media (min-width: ${duandian.zhuomian_da}px) and (hover: hover) and (pointer: fine)`,

  // 所有桌面端
  zhuomian: `@media (hover: hover) and (pointer: fine)`,

  // 非桌面端（手机+平板）
  fei_zhuomian: `@media (max-width: ${duandian.pingban_max}px)`,

  // 触摸设备
  chudian_shebei: `@media (hover: none) and (pointer: coarse)`,

  // 非触摸设备
  fei_chudian_shebei: `@media (hover: hover) and (pointer: fine)`,

  // 大屏平板专用（iPad Pro等）
  da_pingban: `@media (min-width: 1024px) and (max-width: ${duandian.pingban_max}px) and (hover: none) and (pointer: coarse)`
};

/**
 * 设备适配样式生成器
 * 为不同设备生成对应的CSS样式
 */
export const yangshi_shengchengqi = {
  // 导航栏容器样式
  daohanglan_rongqi: () => ({
    shouji: `
      justify-content: space-between;
      padding: 0 var(--jianju-xiao);
      height: 56px;
    `,
    pingban: `
      justify-content: space-between;
      padding: 0 var(--jianju-zhongdeng);
      height: 60px;
    `,
    xiaoping_zhuomian: `
      justify-content: space-between;
      padding: 0 var(--jianju-zhongdeng);
      height: 60px;
    `
  }),

  // Logo图片样式
  logo_tupian: () => ({
    shouji: `
      width: 28px;
      height: 28px;
    `,
    pingban: `
      width: 30px;
      height: 30px;
    `
  }),

  // 网站名称样式
  wangzhan_mingcheng: () => ({
    pingban: `
      font-size: var(--ziti-daxiao-zhongdeng);
    `,
    xiaoping_zhuomian: `
      font-size: var(--ziti-daxiao-zhongdeng);
    `,
    shouji: `
      font-size: var(--ziti-daxiao-xiao);
    `
  }),

  // 侧边栏样式
  cebiankuang: () => ({
    ji_xiao_shouji: `
      width: 100vw;
    `,
    xiao_shouji: `
      width: 85vw;
    `,
    zhongdeng_shouji: `
      width: 75vw;
    `,
    pingban: `
      width: 40vw;
      min-width: 300px;
      max-width: 400px;
    `
  })
};

/**
 * 动态侧边栏宽度计算
 * @param {string} shebeileixing - 设备类型
 * @param {number} kuandu - 屏幕宽度
 * @returns {Object} 包含CSS宽度值和是否显示关闭按钮的配置
 */
export const jisuanCebiankuangKuandu = (shebeileixing_canshu, kuandu) => {
  if (shebeileixing_canshu === shebeileixing.shouji) {
    if (kuandu <= duandian.ji_xiao_shouji) {
      return { kuandu: '100vw', xianshi_guanbi: true }; // 极小屏幕全屏，显示关闭按钮
    } else if (kuandu <= duandian.xiao_shouji) {
      return { kuandu: '100vw', xianshi_guanbi: true }; // 小屏幕全屏，显示关闭按钮
    } else if (kuandu <= duandian.shouji_max) {
      return { kuandu: '85vw', xianshi_guanbi: false }; // 中等手机屏幕85%，不是全屏，不显示关闭按钮
    } else {
      return { kuandu: '75vw', xianshi_guanbi: false }; // 大手机屏幕75%，不是全屏，不显示关闭按钮
    }
  } else if (shebeileixing_canshu === shebeileixing.pingban) {
    return { kuandu: 'min(40vw, 400px)', xianshi_guanbi: false }; // 平板40%但不超过400px，不显示关闭按钮
  } else {
    return { kuandu: '300px', xianshi_guanbi: false }; // 桌面固定宽度，不显示关闭按钮
  }
};

/**
 * 动态菜单间距计算
 * @param {number} kuandu - 屏幕宽度
 * @param {number} caidanshuliang - 菜单项数量
 * @returns {Object} 包含间距和字体大小的配置
 */
export const jisuanCaidanjianju = (kuandu, caidanshuliang = 6) => {
  // 更精确的宽度计算
  const logo_kuandu = Math.min(250, kuandu * 0.15); // Logo区域自适应
  const caozuo_kuandu = 120; // 右侧操作区域宽度
  const bianyuan_jianju = 32; // 边距
  const kexiong_kuandu = kuandu - logo_kuandu - caozuo_kuandu - bianyuan_jianju;

  // 根据菜单项数量和可用宽度计算最佳配置
  const pingjun_kuandu = kexiong_kuandu / caidanshuliang;

  if (pingjun_kuandu >= 120) {
    // 空间非常充足
    return {
      jianju: '20px',
      ziti_daxiao: '14px',
      padding: '10px 18px',
      suofang_bili: 1.1
    };
  } else if (pingjun_kuandu >= 100) {
    // 空间充足
    return {
      jianju: '16px',
      ziti_daxiao: '14px',
      padding: '8px 16px',
      suofang_bili: 1
    };
  } else if (pingjun_kuandu >= 80) {
    // 空间正常
    return {
      jianju: '12px',
      ziti_daxiao: '14px',
      padding: '8px 12px',
      suofang_bili: 0.9
    };
  } else if (pingjun_kuandu >= 65) {
    // 空间紧张
    return {
      jianju: '8px',
      ziti_daxiao: '13px',
      padding: '6px 10px',
      suofang_bili: 0.8
    };
  } else if (pingjun_kuandu >= 50) {
    // 空间很紧张
    return {
      jianju: '6px',
      ziti_daxiao: '12px',
      padding: '5px 8px',
      suofang_bili: 0.7
    };
  } else {
    // 空间极度紧张
    return {
      jianju: '4px',
      ziti_daxiao: '11px',
      padding: '4px 6px',
      suofang_bili: 0.6
    };
  }
};

/**
 * 动态菜单配置Hook
 * @param {number} caidanshuliang - 菜单项数量
 * @returns {Object} 菜单配置信息
 */
export const useDongtaiCaidanPeizhi = (caidanshuliang = 6) => {
  const huoqu_caidanpeizhi = useMemoizedFn(() => {
    const kuandu = window.innerWidth;
    const shebei = huoqu_shebeitexing();

    // 只对桌面设备进行动态调整
    if (shebei.zhichi_hover && shebei.zhichi_pointer) {
      return jisuanCaidanjianju(kuandu, caidanshuliang);
    }

    // 非桌面设备返回默认配置
    return {
      jianju: '16px',
      ziti_daxiao: '14px',
      padding: '8px 16px',
      suofang_bili: 1
    };
  });

  return { huoqu_caidanpeizhi };
};

/**
 * 动态高度计算工具
 * 计算可用的屏幕高度，排除导航栏等固定元素
 */
export const jisuanDongtaiGaodu = () => {
  const pingmugaodu = window.innerHeight;
  const daohanglan_gaodu = 60; // 导航栏高度
  const dingbu_jianju = 20; // 顶部间距
  const dibu_jianju = 20; // 底部间距

  // 计算可用高度
  const keyong_gaodu = pingmugaodu - daohanglan_gaodu - dingbu_jianju - dibu_jianju;

  return {
    pingmugaodu,
    keyong_gaodu,
    daohanglan_gaodu,
    dingbu_jianju,
    dibu_jianju
  };
};

/**
 * 动态高度计算Hook
 * 提供响应式的高度计算功能
 */
export const useDongtaiGaoduJisuan = () => {
  const [gaodupeizhi, shezhigaodupeizhi] = React.useState(() => jisuanDongtaiGaodu());

  const gengxingaodupeizhi = useMemoizedFn(() => {
    const xinpeizhi = jisuanDongtaiGaodu();
    shezhigaodupeizhi(xinpeizhi);
  });

  React.useEffect(() => {
    // 监听窗口大小变化
    const chulichuangkoubiandong = () => {
      gengxingaodupeizhi();
    };

    window.addEventListener('resize', chulichuangkoubiandong);
    window.addEventListener('orientationchange', chulichuangkoubiandong);

    return () => {
      window.removeEventListener('resize', chulichuangkoubiandong);
      window.removeEventListener('orientationchange', chulichuangkoubiandong);
    };
  }, [gengxingaodupeizhi]);

  return {
    gaodupeizhi,
    gengxingaodupeizhi
  };
};
