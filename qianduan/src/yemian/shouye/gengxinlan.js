import React, { useState } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { meiti_chaxun } from '../../gongju/shebeishiPei_gongju.js';

// 更新栏容器
const Gengxinlanrongqi = styled.div`
  width: 480px;
  background: ${props => props.theme.mingcheng === 'anhei'
    ? `linear-gradient(135deg, ${props.theme.yanse.danjinse}15, ${props.theme.yanse.danjinse_qian}10)`
    : props.theme.yanse.biaomian};
  border: 1px solid ${props => props.theme.mingcheng === 'anhei'
    ? `${props.theme.yanse.danjinse}40`
    : props.theme.yanse.biankuang};
  border-radius: 15px;
  overflow: hidden;
  margin-top: 8px;
  margin-bottom: 3px;
  box-shadow: ${props => props.theme.mingcheng === 'anhei'
    ? `0 4px 12px ${props.theme.yanse.danjinse}20, ${props.theme.yinying.xiao}`
    : props.theme.yinying.xiao};

  ${meiti_chaxun.shouji} {
    width: 100%;
    max-width: none;
    border-radius: 12px;
  }

  ${meiti_chaxun.pingban} {
    width: 100%;
    max-width: none;
    border-radius: 12px;
  }
`;

// 标签栏容器
const Biaoqianlanrongqi = styled.div`
  display: flex;
  background: ${props => props.theme.mingcheng === 'anhei'
    ? `${props.theme.yanse.danjinse}08`
    : 'rgba(0, 0, 0, 0.03)'};
  border-bottom: 1px solid ${props => props.theme.mingcheng === 'anhei'
    ? `${props.theme.yanse.danjinse}30`
    : 'rgba(0, 0, 0, 0.1)'};
`;

// 标签项
const Biaoqianxiang = styled.button.withConfig({
  shouldForwardProp: (prop) => prop !== 'huoyue'
})`
  flex: 1;
  padding: 12px 16px;
  background: transparent;
  border: none;
  color: ${props => props.huoyue 
    ? props.theme.yanse.wenzi_zhuyao
    : props.theme.yanse.wenzi_ciyao};
  font-size: ${props => props.theme.ziti.daxiao.xiao};
  font-weight: ${props => props.huoyue 
    ? props.theme.ziti.zhongliang.zhongdeng
    : props.theme.ziti.zhongliang.putong};
  cursor: pointer;
  transition: all ${props => props.theme.donghua.sujian.kuai} ${props => props.theme.donghua.huanman.biaozhun};
  position: relative;
  
  &:hover {
    color: ${props => props.theme.mingcheng === 'anhei'
      ? props.theme.yanse.danjinse_hou
      : props.theme.yanse.wenzi_zhuyao};
    background: ${props => props.theme.mingcheng === 'anhei'
      ? `${props.theme.yanse.danjinse}15`
      : 'rgba(0, 0, 0, 0.05)'};
  }

  ${props => props.huoyue && `
    color: ${props.theme.mingcheng === 'anhei'
      ? props.theme.yanse.danjinse
      : props.theme.yanse.wenzi_zhuyao};
    background: ${props.theme.mingcheng === 'anhei'
      ? `${props.theme.yanse.danjinse}20`
      : 'rgba(0, 0, 0, 0.08)'};
  `}

  ${meiti_chaxun.shouji} {
    padding: 10px 12px;
    font-size: ${props => props.theme.ziti.daxiao.xiaoxiao};
  }

  ${meiti_chaxun.pingban} {
    padding: 10px 12px;
    font-size: ${props => props.theme.ziti.daxiao.xiaoxiao};
  }
`;

// 内容区域
const Neirongquyu = styled.div`
  padding: 16px;
  height: calc(100vh - 80px - 270px - 60px - 10px);
  overflow-y: auto;

  /* 隐藏滚动条但保留滚动功能 */
  &::-webkit-scrollbar {
    display: none;
  }

  /* Firefox */
  scrollbar-width: none;



  ${meiti_chaxun.shouji} {
    padding: 12px;
    height: calc(100vh - 80px - 180px - 60px - 10px);
    overflow-y: auto;

    /* 手机端隐藏滚动条 */
    &::-webkit-scrollbar {
      display: none;
    }

    scrollbar-width: none;
  }

  ${meiti_chaxun.pingban} {
    padding: 12px;
    height: calc(100vh - 80px - 220px - 60px - 10px);
    overflow-y: auto;

    /* 平板端隐藏滚动条 */
    &::-webkit-scrollbar {
      display: none;
    }

    scrollbar-width: none;
  }
`;

// 更新项
const Gengxinxiang = styled(motion.div)`
  display: flex;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid ${props => props.theme.mingcheng === 'anhei'
    ? `${props.theme.yanse.danjinse}15`
    : 'rgba(0, 0, 0, 0.05)'};

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background: ${props => props.theme.mingcheng === 'anhei'
      ? `${props.theme.yanse.danjinse}08`
      : 'rgba(0, 0, 0, 0.02)'};
    border-radius: 8px;
    margin: 0 -8px;
    padding: 12px 8px;
  }
`;

// 更新图标
const Gengxintubiao = styled.div`
  width: 24px;
  height: 24px;
  border-radius: 4px;
  margin-right: 12px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: ${props => props.theme.ziti.zhongliang.cuhei};
  color: white;
  background: ${props => {
    switch (props.leixing) {
      case 'gonggao': return '#10b981'; // 绿色 - 维护公告
      case 'huodong': return '#f59e0b'; // 橙色 - 游戏活动
      case 'xinwen': return '#3b82f6'; // 蓝色 - 版本资讯
      case 'xitong': return '#8b5cf6'; // 紫色 - 系统更新
      case 'zhiye': return '#ef4444'; // 红色 - 职业平衡
      case 'zhuangbei': return '#06b6d4'; // 青色 - 装备道具
      default: return '#6b7280'; // 灰色
    }
  }};

  ${meiti_chaxun.shouji} {
    width: 20px;
    height: 20px;
    font-size: 10px;
    margin-right: 8px;
  }

  ${meiti_chaxun.pingban} {
    width: 20px;
    height: 20px;
    font-size: 10px;
    margin-right: 8px;
  }
`;

// 更新内容
const Gengxinneirong = styled.div`
  flex: 1;
  min-width: 0;
`;

// 更新标题
const Gengxinbiaoti = styled.div`
  font-size: ${props => props.theme.ziti.daxiao.xiao};
  font-weight: ${props => props.theme.ziti.zhongliang.zhongdeng};
  color: ${props => props.theme.yanse.wenzi_zhuyao};
  margin-bottom: 4px;
  line-height: 1.4;
  
  /* 文本溢出处理 */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;

  ${meiti_chaxun.shouji} {
    font-size: ${props => props.theme.ziti.daxiao.xiaoxiao};
  }

  ${meiti_chaxun.pingban} {
    font-size: ${props => props.theme.ziti.daxiao.xiaoxiao};
  }
`;

// 更新时间
const Gengxinshijian = styled.div`
  font-size: ${props => props.theme.ziti.daxiao.xiaoxiao};
  color: ${props => props.theme.yanse.wenzi_ciyao};
  
  ${meiti_chaxun.shouji} {
    font-size: 10px;
  }

  ${meiti_chaxun.pingban} {
    font-size: 10px;
  }
`;

// 空状态
const Kongzhuangtai = styled.div`
  text-align: center;
  padding: 40px 20px;
  color: ${props => props.theme.yanse.wenzi_ciyao};
  font-size: ${props => props.theme.ziti.daxiao.xiao};
`;

// 更新栏组件
function Gengxinlan() {
  const [huoyuebiaoqian, shehuoyuebiaoqian] = useState('suoyou');

  // 仙境传说游戏更新数据
  const gengxinshuju = [
    {
      id: 1,
      leixing: 'gonggao',
      biaoti: '2025年8月5日例行维护公告',
      shijian: '2025.08.04 08:30',
      biaoqian: ['suoyou', 'gonggao']
    },
    {
      id: 2,
      leixing: 'zhuangbei',
      biaoti: '新装备【天使之翼】实装！获取方式公开',
      shijian: '2025.08.03 14:20',
      biaoqian: ['suoyou', 'zhuangbei']
    },
    {
      id: 3,
      leixing: 'huodong',
      biaoti: '夏日祭典活动开启！限时MVP挑战',
      shijian: '2025.08.02 10:00',
      biaoqian: ['suoyou', 'huodong']
    },
    {
      id: 4,
      leixing: 'xinwen',
      biaoti: '新地图【冰雪王国】探索指南发布',
      shijian: '2025.08.01 16:45',
      biaoqian: ['suoyou', 'xinwen']
    },
    {
      id: 5,
      leixing: 'zhiye',
      biaoti: '法师系职业技能平衡性调整说明',
      shijian: '2025.07.30 11:30',
      biaoqian: ['suoyou', 'zhiye']
    },
    {
      id: 6,
      leixing: 'gonggao',
      biaoti: '反外挂系统升级维护通知',
      shijian: '2025.07.29 08:15',
      biaoqian: ['suoyou', 'gonggao']
    },
    {
      id: 7,
      leixing: 'huodong',
      biaoti: '公会战赛季正式开启！丰厚奖励等你来拿',
      shijian: '2025.07.28 20:00',
      biaoqian: ['suoyou', 'huodong']
    },
    {
      id: 8,
      leixing: 'zhuangbei',
      biaoti: '传说武器【雷神之锤】锻造材料调整',
      shijian: '2025.07.27 13:45',
      biaoqian: ['suoyou', 'zhuangbei']
    },
    {
      id: 9,
      leixing: 'xinwen',
      biaoti: '新职业【神官】转职任务详细攻略',
      shijian: '2025.07.26 15:20',
      biaoqian: ['suoyou', 'xinwen']
    },
    {
      id: 10,
      leixing: 'xitong',
      biaoti: '交易系统界面优化更新',
      shijian: '2025.07.25 09:30',
      biaoqian: ['suoyou', 'xitong']
    },
    {
      id: 11,
      leixing: 'huodong',
      biaoti: '波利岛探险活动限时开放',
      shijian: '2025.07.24 12:00',
      biaoqian: ['suoyou', 'huodong']
    },
    {
      id: 12,
      leixing: 'zhiye',
      biaoti: '骑士职业【圣十字斩击】技能效果调整',
      shijian: '2025.07.23 14:15',
      biaoqian: ['suoyou', 'zhiye']
    },
    {
      id: 13,
      leixing: 'xinwen',
      biaoti: '新副本【古代遗迹】开放预告',
      shijian: '2025.07.22 17:30',
      biaoqian: ['suoyou', 'xinwen']
    },
    {
      id: 14,
      leixing: 'zhuangbei',
      biaoti: '稀有卡片【黄金虫卡】掉落率提升',
      shijian: '2025.07.21 10:45',
      biaoqian: ['suoyou', 'zhuangbei']
    },
    {
      id: 15,
      leixing: 'gonggao',
      biaoti: '服务器负载优化维护完成',
      shijian: '2025.07.20 08:00',
      biaoqian: ['suoyou', 'gonggao']
    },
    {
      id: 16,
      leixing: 'huodong',
      biaoti: '魔物讨伐令活动火热进行中',
      shijian: '2025.07.19 16:20',
      biaoqian: ['suoyou', 'huodong']
    },
    {
      id: 17,
      leixing: 'xitong',
      biaoti: '宠物系统新增自动喂食功能',
      shijian: '2025.07.18 11:10',
      biaoqian: ['suoyou', 'xitong']
    },
    {
      id: 18,
      leixing: 'xinwen',
      biaoti: '新怪物【冰霜巨龙】登场！挑战等级90+',
      shijian: '2025.07.17 19:45',
      biaoqian: ['suoyou', 'xinwen']
    },
    {
      id: 19,
      leixing: 'zhiye',
      biaoti: '盗贼系职业【音速投掷】技能重做',
      shijian: '2025.07.16 13:25',
      biaoqian: ['suoyou', 'zhiye']
    },
    {
      id: 20,
      leixing: 'zhuangbei',
      biaoti: '头饰装备【猫咪头巾】限时返场',
      shijian: '2025.07.15 15:00',
      biaoqian: ['suoyou', 'zhuangbei']
    },
    {
      id: 21,
      leixing: 'huodong',
      biaoti: '普隆德拉夏日庆典开幕式',
      shijian: '2025.07.14 18:30',
      biaoqian: ['suoyou', 'huodong']
    },
    {
      id: 22,
      leixing: 'gonggao',
      biaoti: '游戏客户端安全更新通知',
      shijian: '2025.07.13 09:15',
      biaoqian: ['suoyou', 'gonggao']
    },
    {
      id: 23,
      leixing: 'xinwen',
      biaoti: '新地图【梦罗克地下城】探索攻略',
      shijian: '2025.07.12 14:40',
      biaoqian: ['suoyou', 'xinwen']
    },
    {
      id: 24,
      leixing: 'xitong',
      biaoti: '好友系统聊天功能增强',
      shijian: '2025.07.11 12:20',
      biaoqian: ['suoyou', 'xitong']
    },
    {
      id: 25,
      leixing: 'zhuangbei',
      biaoti: '武器精炼成功率调整公告',
      shijian: '2025.07.10 16:50',
      biaoqian: ['suoyou', 'zhuangbei']
    }
  ];

  // 标签配置
  const biaoqianpeizhi = [
    { jian: 'suoyou', mingcheng: '全部' },
    { jian: 'gonggao', mingcheng: '公告' },
    { jian: 'huodong', mingcheng: '活动' },
    { jian: 'xinwen', mingcheng: '资讯' },
    { jian: 'zhiye', mingcheng: '职业' },
    { jian: 'zhuangbei', mingcheng: '装备' },
    { jian: 'xitong', mingcheng: '系统' }
  ];

  // 图标映射
  const tubiaoying = {
    gonggao: '公',
    huodong: '活',
    xinwen: '资',
    xitong: '系',
    zhiye: '职',
    zhuangbei: '装'
  };

  // 过滤数据
  const guolvshuju = gengxinshuju.filter(item => 
    huoyuebiaoqian === 'suoyou' || item.biaoqian.includes(huoyuebiaoqian)
  );

  return (
    <Gengxinlanrongqi>
      {/* 标签栏 */}
      <Biaoqianlanrongqi>
        {biaoqianpeizhi.map(biaoqian => (
          <Biaoqianxiang
            key={biaoqian.jian}
            huoyue={huoyuebiaoqian === biaoqian.jian}
            onClick={() => shehuoyuebiaoqian(biaoqian.jian)}
          >
            {biaoqian.mingcheng}
          </Biaoqianxiang>
        ))}
      </Biaoqianlanrongqi>

      {/* 内容区域 */}
      <Neirongquyu>
        <AnimatePresence mode="wait">
          {guolvshuju.length > 0 ? (
            <motion.div
              key={huoyuebiaoqian}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
            >
              {guolvshuju.map(item => (
                <Gengxinxiang
                  key={item.id}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: 0.05 }}
                >
                  <Gengxintubiao leixing={item.leixing}>
                    {tubiaoying[item.leixing]}
                  </Gengxintubiao>
                  <Gengxinneirong>
                    <Gengxinbiaoti>{item.biaoti}</Gengxinbiaoti>
                    <Gengxinshijian>{item.shijian}</Gengxinshijian>
                  </Gengxinneirong>
                </Gengxinxiang>
              ))}
            </motion.div>
          ) : (
            <Kongzhuangtai>
              暂无{biaoqianpeizhi.find(b => b.jian === huoyuebiaoqian)?.mingcheng}更新
            </Kongzhuangtai>
          )}
        </AnimatePresence>
      </Neirongquyu>
    </Gengxinlanrongqi>
  );
}

export default Gengxinlan;
