import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { meiti_chaxun, useShebeileixingJiance } from '../../gongju/shebeishiPei_gongju.js';
import { wangguanpeizhi } from '../../peizhi/wangguanpeizhi.js';

// 怪物列表容器
const Guaiwuliebiaorongqi = styled.div`
  width: 100%;
  background: ${props => props.theme.mingcheng === 'anhei'
    ? `linear-gradient(145deg, ${props.theme.yanse.danjinse}12, ${props.theme.yanse.danjinse_qian}08, transparent)`
    : `linear-gradient(145deg, rgba(255,255,255,0.95), rgba(248,250,252,0.9))`};
  border: 1px solid ${props => props.theme.mingcheng === 'anhei'
    ? `${props.theme.yanse.danjinse}35`
    : 'rgba(226,232,240,0.8)'};
  border-radius: 20px;
  overflow: hidden;
  margin-bottom: 24px;
  box-shadow: ${props => props.theme.mingcheng === 'anhei'
    ? `0 8px 32px ${props.theme.yanse.danjinse}15, 0 2px 8px rgba(0,0,0,0.1)`
    : '0 8px 32px rgba(0,0,0,0.06), 0 2px 8px rgba(0,0,0,0.04)'};
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: ${props => props.theme.mingcheng === 'anhei'
      ? `linear-gradient(90deg, transparent, ${props.theme.yanse.danjinse}40, transparent)`
      : 'linear-gradient(90deg, transparent, rgba(59,130,246,0.3), transparent)'};
  }

  ${meiti_chaxun.shouji} {
    width: 100%;
    max-width: none;
    border-radius: 16px;
    margin-bottom: 16px;
    margin-left: 0;
    margin-right: 0;
  }

  ${meiti_chaxun.pingban} {
    border-radius: 18px;
    margin-bottom: 20px;
    max-width: 100%;
  }

  ${meiti_chaxun.zhuomian} {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
  }
`;

// 标题区域
const Biaotiquyu = styled.div`
  padding: 20px 24px;
  border-bottom: 1px solid ${props => props.theme.mingcheng === 'anhei'
    ? `${props.theme.yanse.danjinse}25`
    : 'rgba(226,232,240,0.6)'};
  display: flex;
  align-items: center;
  gap: 16px;
  background: ${props => props.theme.mingcheng === 'anhei'
    ? `linear-gradient(135deg, ${props.theme.yanse.danjinse}08, transparent)`
    : 'linear-gradient(135deg, rgba(255,255,255,0.8), rgba(248,250,252,0.4))'};
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 24px;
    right: 24px;
    height: 1px;
    background: ${props => props.theme.mingcheng === 'anhei'
      ? `linear-gradient(90deg, transparent, ${props.theme.yanse.danjinse}30, transparent)`
      : 'linear-gradient(90deg, transparent, rgba(59,130,246,0.2), transparent)'};
  }

  ${meiti_chaxun.shouji} {
    padding: 16px 20px;
    gap: 12px;

    &::after {
      left: 20px;
      right: 20px;
    }
  }

  ${meiti_chaxun.pingban} {
    padding: 18px 22px;
    gap: 14px;

    &::after {
      left: 22px;
      right: 22px;
    }
  }
`;

// 怪物数据图标
const Guaiwutubiao = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background: ${props => props.theme.mingcheng === 'anhei'
    ? `linear-gradient(135deg, ${props.theme.yanse.danjinse}90, ${props.theme.yanse.danjinse_qian}70)`
    : 'linear-gradient(135deg, #3b82f6, #1d4ed8)'};
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
  font-weight: ${props => props.theme.ziti.zhongliang.cuhei};
  flex-shrink: 0;
  box-shadow: ${props => props.theme.mingcheng === 'anhei'
    ? `0 4px 12px ${props.theme.yanse.danjinse}30`
    : '0 4px 12px rgba(59,130,246,0.25)'};
  position: relative;

  &::before {
    content: '';
    position: absolute;
    inset: 1px;
    border-radius: 11px;
    background: ${props => props.theme.mingcheng === 'anhei'
      ? `linear-gradient(135deg, ${props.theme.yanse.danjinse}95, ${props.theme.yanse.danjinse_qian}75)`
      : 'linear-gradient(135deg, #60a5fa, #3b82f6)'};
  }

  span {
    position: relative;
    z-index: 1;
  }

  ${meiti_chaxun.shouji} {
    width: 32px;
    height: 32px;
    font-size: 16px;
    border-radius: 10px;

    &::before {
      border-radius: 9px;
    }
  }

  ${meiti_chaxun.pingban} {
    width: 36px;
    height: 36px;
    font-size: 17px;
    border-radius: 11px;

    &::before {
      border-radius: 10px;
    }
  }
`;

// 标题文字
const Biaotiwenzi = styled.h3`
  margin: 0;
  font-size: ${props => props.theme.ziti.daxiao.da};
  font-weight: ${props => props.theme.ziti.zhongliang.cuhei};
  color: ${props => props.theme.yanse.wenzi_zhuyao};
  flex: 1;
  background: ${props => props.theme.mingcheng === 'anhei'
    ? `linear-gradient(135deg, ${props.theme.yanse.danjinse}95, ${props.theme.yanse.danjinse_qian}80)`
    : 'linear-gradient(135deg, #1e293b, #475569)'};
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: 0.5px;

  ${meiti_chaxun.shouji} {
    font-size: ${props => props.theme.ziti.daxiao.zhongdeng};
    letter-spacing: 0.3px;
  }

  ${meiti_chaxun.pingban} {
    font-size: ${props => props.theme.ziti.daxiao.zhongdeng};
    letter-spacing: 0.4px;
  }
`;

// 怪物列表滚动容器
const Guaiwugundongrongqi = styled.div`
  padding: 16px 20px 16px 0; /* 去掉左边的padding */
  overflow-x: auto;
  overflow-y: hidden;
  width: 100%;
  max-width: 100%; /* 使用父容器的宽度限制 */

  /* 隐藏滚动条但保留滚动功能 */
  &::-webkit-scrollbar {
    display: none;
  }
  scrollbar-width: none;

  ${meiti_chaxun.shouji} {
    padding: 10px 0; /* 完全去掉左右padding */
    max-width: 100%;
  }

  ${meiti_chaxun.pingban} {
    padding: 14px 18px 14px 0; /* 去掉左边的padding */
    max-width: 100%;
  }

  /* 桌面端确保不超出父容器 */
  ${meiti_chaxun.zhuomian} {
    max-width: 100%;
    padding-right: 20px; /* 保留右边距，避免贴边 */
  }
`;

// 怪物列表容器
const Guaiwuliebiaoliebiao = styled.div`
  display: flex;
  gap: 12px;
  min-width: fit-content;
  margin-left: 0; /* 确保没有左边距 */
  padding-left: 0; /* 彻底去掉左边的内边距 */

  ${meiti_chaxun.shouji} {
    gap: 8px;
    padding-left: 0;
  }

  ${meiti_chaxun.pingban} {
    gap: 10px;
    padding-left: 0;
  }
`;

// 怪物卡片
const Guaiwukapian = styled(motion.div)`
  min-width: 160px;
  width: 160px;
  background: ${props => props.theme.mingcheng === 'anhei'
    ? `linear-gradient(145deg, ${props.theme.yanse.danjinse}12, ${props.theme.yanse.danjinse_qian}06, transparent)`
    : 'linear-gradient(145deg, rgba(255,255,255,0.95), rgba(248,250,252,0.8))'};
  border: 1px solid ${props => props.theme.mingcheng === 'anhei'
    ? `${props.theme.yanse.danjinse}25`
    : 'rgba(226,232,240,0.6)'};
  border-radius: 12px;
  padding: 14px;
  cursor: pointer;
  transition: all ${props => props.theme.donghua.sujian.zhongdeng} ${props => props.theme.donghua.huanman.biaozhun};
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  flex-shrink: 0;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: ${props => props.theme.mingcheng === 'anhei'
      ? `linear-gradient(90deg, transparent, ${props.theme.yanse.danjinse}40, transparent)`
      : 'linear-gradient(90deg, transparent, rgba(59,130,246,0.3), transparent)'};
  }

  &:hover {
    transform: translateY(-2px) scale(1.01);
    box-shadow: ${props => props.theme.mingcheng === 'anhei'
      ? `0 8px 24px ${props.theme.yanse.danjinse}20, 0 2px 8px rgba(0,0,0,0.1)`
      : '0 8px 24px rgba(0,0,0,0.06), 0 2px 8px rgba(59,130,246,0.12)'};
    border-color: ${props => props.theme.mingcheng === 'anhei'
      ? `${props.theme.yanse.danjinse}50`
      : 'rgba(59,130,246,0.4)'};
    background: ${props => props.theme.mingcheng === 'anhei'
      ? `linear-gradient(145deg, ${props.theme.yanse.danjinse}15, ${props.theme.yanse.danjinse_qian}08, transparent)`
      : 'linear-gradient(145deg, rgba(255,255,255,0.98), rgba(248,250,252,0.9))'};
  }

  ${meiti_chaxun.shouji} {
    min-width: 120px;
    width: 120px;
    padding: 10px;
    border-radius: 10px;

    &:hover {
      transform: scale(1.01);
    }
  }

  ${meiti_chaxun.pingban} {
    min-width: 140px;
    width: 140px;
    padding: 12px;
    border-radius: 11px;
  }
`;

// 怪物头像
const Guaiwutouxiang = styled.div`
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: ${props => props.theme.mingcheng === 'anhei'
    ? `linear-gradient(135deg, ${props.theme.yanse.danjinse}25, ${props.theme.yanse.danjinse_qian}15)`
    : 'linear-gradient(135deg, rgba(59,130,246,0.15), rgba(147,197,253,0.1))'};
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: ${props => props.theme.yanse.wenzi_zhuyao};
  margin: 0 auto 10px auto;
  border: 1.5px solid ${props => props.theme.mingcheng === 'anhei'
    ? `${props.theme.yanse.danjinse}35`
    : 'rgba(59,130,246,0.2)'};
  box-shadow: ${props => props.theme.mingcheng === 'anhei'
    ? `0 2px 8px ${props.theme.yanse.danjinse}15, inset 0 1px 0 ${props.theme.yanse.danjinse}25`
    : '0 2px 8px rgba(59,130,246,0.08), inset 0 1px 0 rgba(255,255,255,0.4)'};
  position: relative;
  transition: all 0.3s ease;

  &::before {
    content: '';
    position: absolute;
    inset: 1.5px;
    border-radius: 10px;
    background: ${props => props.theme.mingcheng === 'anhei'
      ? `linear-gradient(135deg, ${props.theme.yanse.danjinse}15, ${props.theme.yanse.danjinse_qian}08)`
      : 'linear-gradient(135deg, rgba(255,255,255,0.8), rgba(248,250,252,0.6))'};
  }

  span {
    position: relative;
    z-index: 1;
  }

  ${meiti_chaxun.shouji} {
    width: 36px;
    height: 36px;
    font-size: 16px;
    margin-bottom: 8px;
    border-radius: 10px;
    border-width: 1px;

    &::before {
      border-radius: 9px;
    }
  }

  ${meiti_chaxun.pingban} {
    width: 42px;
    height: 42px;
    font-size: 18px;
    margin-bottom: 9px;
    border-radius: 11px;

    &::before {
      border-radius: 10px;
    }
  }
`;

// 怪物名称
const Guaiwumingcheng = styled.div`
  font-size: ${props => props.theme.ziti.daxiao.xiao};
  font-weight: ${props => props.theme.ziti.zhongliang.cuhei};
  color: ${props => props.theme.yanse.wenzi_zhuyao};
  text-align: center;
  margin-bottom: 8px;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  letter-spacing: 0.2px;
  background: ${props => props.theme.mingcheng === 'anhei'
    ? `linear-gradient(135deg, ${props.theme.yanse.danjinse}90, ${props.theme.yanse.danjinse_qian}70)`
    : 'linear-gradient(135deg, #1e293b, #475569)'};
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;

  ${meiti_chaxun.shouji} {
    font-size: ${props => props.theme.ziti.daxiao.xiaoxiao};
    margin-bottom: 6px;
    letter-spacing: 0.1px;
  }

  ${meiti_chaxun.pingban} {
    font-size: ${props => props.theme.ziti.daxiao.xiaoxiao};
    margin-bottom: 7px;
    letter-spacing: 0.15px;
  }
`;

// 怪物等级
const Guaiwudengji = styled.div`
  font-size: ${props => props.theme.ziti.daxiao.xiaoxiao};
  color: ${props => props.theme.mingcheng === 'anhei'
    ? `${props.theme.yanse.danjinse}95`
    : '#3b82f6'};
  text-align: center;
  background: ${props => props.theme.mingcheng === 'anhei'
    ? `linear-gradient(135deg, ${props.theme.yanse.danjinse}15, ${props.theme.yanse.danjinse_qian}08)`
    : 'linear-gradient(135deg, rgba(59,130,246,0.1), rgba(147,197,253,0.05))'};
  padding: 4px 8px;
  border-radius: 6px;
  font-weight: ${props => props.theme.ziti.zhongliang.cuhei};
  border: 1px solid ${props => props.theme.mingcheng === 'anhei'
    ? `${props.theme.yanse.danjinse}25`
    : 'rgba(59,130,246,0.2)'};
  letter-spacing: 0.3px;
  box-shadow: ${props => props.theme.mingcheng === 'anhei'
    ? `0 1px 3px ${props.theme.yanse.danjinse}12`
    : '0 1px 3px rgba(59,130,246,0.08)'};

  ${meiti_chaxun.shouji} {
    font-size: 10px;
    padding: 3px 6px;
    border-radius: 5px;
    letter-spacing: 0.2px;
  }

  ${meiti_chaxun.pingban} {
    font-size: 11px;
    padding: 3px 7px;
    border-radius: 5px;
    letter-spacing: 0.25px;
  }
`;

// 加载状态
const Jiazaizhuangtai = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: ${props => props.theme.yanse.wenzi_ciyao};
  font-size: ${props => props.theme.ziti.daxiao.zhongdeng};

  ${meiti_chaxun.shouji} {
    padding: 30px 16px;
    font-size: ${props => props.theme.ziti.daxiao.xiao};
  }
`;

// 错误状态
const Cuowuzhuangtai = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: ${props => props.theme.yanse.cuowu};
  font-size: ${props => props.theme.ziti.daxiao.zhongdeng};

  ${meiti_chaxun.shouji} {
    padding: 30px 16px;
    font-size: ${props => props.theme.ziti.daxiao.xiao};
  }
`;

// 怪物列表组件
function Guaiwuliebiao() {
  const [guaiwushuju, shezhi_guaiwushuju] = useState([]);
  const [jiazaizhong, shezhi_jiazaizhong] = useState(true);
  const [cuowu, shezhi_cuowu] = useState(null);

  // 滚动相关状态和引用
  const gundongrongqiref = useRef(null);
  const [zhengzaigundong, shezhi_zhengzaigundong] = useState(false);
  const [shubiaoruyu, shezhi_shubiaoruyu] = useState(false);
  const gundongdonghuaref = useRef(null);

  // 设备检测
  const { shuchu_tiaoshi_xinxi } = useShebeileixingJiance();

  // 模拟怪物数据（临时使用）
  const moniGuaiwushuju = [
    { guaiwu_id: 1001, guaiwu_mingcheng: '波利', level: 1, yuansu: ['无'], zhongzu: ['植物'] },
    { guaiwu_id: 1002, guaiwu_mingcheng: '小强', level: 2, yuansu: ['地'], zhongzu: ['昆虫'] },
    { guaiwu_id: 1003, guaiwu_mingcheng: '蘑菇宝贝', level: 3, yuansu: ['地'], zhongzu: ['植物'] },
    { guaiwu_id: 1004, guaiwu_mingcheng: '红蝙蝠', level: 5, yuansu: ['暗'], zhongzu: ['动物'] },
    { guaiwu_id: 1005, guaiwu_mingcheng: '绿水母', level: 4, yuansu: ['水'], zhongzu: ['鱼贝'] },
    { guaiwu_id: 1006, guaiwu_mingcheng: '小鸡', level: 1, yuansu: ['无'], zhongzu: ['动物'] },
    { guaiwu_id: 1007, guaiwu_mingcheng: '蚯蚓', level: 2, yuansu: ['地'], zhongzu: ['昆虫'] },
    { guaiwu_id: 1008, guaiwu_mingcheng: '树精', level: 6, yuansu: ['地'], zhongzu: ['植物'] },
    { guaiwu_id: 1009, guaiwu_mingcheng: '火狐', level: 8, yuansu: ['火'], zhongzu: ['动物'] },
    { guaiwu_id: 1010, guaiwu_mingcheng: '冰晶', level: 7, yuansu: ['水'], zhongzu: ['无机物'] },
    { guaiwu_id: 1011, guaiwu_mingcheng: '雷鸟', level: 10, yuansu: ['风'], zhongzu: ['动物'] },
    { guaiwu_id: 1012, guaiwu_mingcheng: '石巨人', level: 12, yuansu: ['地'], zhongzu: ['无机物'] },
    { guaiwu_id: 1013, guaiwu_mingcheng: '暗影', level: 15, yuansu: ['暗'], zhongzu: ['恶魔'] },
    { guaiwu_id: 1014, guaiwu_mingcheng: '光精灵', level: 14, yuansu: ['圣'], zhongzu: ['精灵'] },
    { guaiwu_id: 1015, guaiwu_mingcheng: '毒蛇', level: 9, yuansu: ['毒'], zhongzu: ['动物'] },
    { guaiwu_id: 1016, guaiwu_mingcheng: '钢铁兽', level: 18, yuansu: ['无'], zhongzu: ['机械'] }
  ];

  // 获取怪物头像表情符号
  const huoquguaiwubiaoqing = (mingcheng) => {
    const biaoqingying = {
      '波利': '🟢',
      '小强': '🪲',
      '蘑菇宝贝': '🍄',
      '红蝙蝠': '🦇',
      '绿水母': '🪼',
      '小鸡': '🐣',
      '蚯蚓': '🪱',
      '树精': '🌳',
      '火狐': '🦊',
      '冰晶': '❄️',
      '雷鸟': '⚡',
      '石巨人': '🗿',
      '暗影': '👤',
      '光精灵': '✨',
      '毒蛇': '🐍',
      '钢铁兽': '🤖'
    };
    return biaoqingying[mingcheng] || '👾';
  };

  // 检查是否支持鼠标悬浮滚动
  const zhichishubiaogundongg = () => {
    // 检测是否支持hover和精确指针
    const zhichi_hover = window.matchMedia('(hover: hover)').matches;
    const zhichi_pointer = window.matchMedia('(pointer: fine)').matches;
    return zhichi_hover && zhichi_pointer;
  };

  // 平滑滚动函数
  const pinghuagundong = (mubiaoweizhii, shijian = 300) => {
    const rongqi = gundongrongqiref.current;
    if (!rongqi) return;

    const kaishiweizhii = rongqi.scrollLeft;
    const jianli = mubiaoweizhii - kaishiweizhii;
    const kaishishijian = performance.now();

    const donghua = (dangqianshijian) => {
      const jindu = Math.min((dangqianshijian - kaishishijian) / shijian, 1);
      const huanmancurve = 1 - Math.pow(1 - jindu, 3); // easeOut cubic

      rongqi.scrollLeft = kaishiweizhii + jianli * huanmancurve;

      if (jindu < 1) {
        gundongdonghuaref.current = requestAnimationFrame(donghua);
      } else {
        shezhi_zhengzaigundong(false);
      }
    };

    if (gundongdonghuaref.current) {
      cancelAnimationFrame(gundongdonghuaref.current);
    }

    shezhi_zhengzaigundong(true);
    gundongdonghuaref.current = requestAnimationFrame(donghua);
  };



  // 处理鼠标进入事件
  const chulishubiaoruyu = () => {
    const zhichi = zhichishubiaogundongg();

    if (zhichi) {
      shezhi_shubiaoruyu(true);
    }
  };

  // 处理鼠标离开事件
  const chulishubiaolikai = () => {
    shezhi_shubiaoruyu(false);
    if (gundongdonghuaref.current) {
      cancelAnimationFrame(gundongdonghuaref.current);
      shezhi_zhengzaigundong(false);
    }
  };

  // 组件挂载时加载数据
  useEffect(() => {
    const jiazaiguaiwushuju = async () => {
      try {
        shezhi_jiazaizhong(true);
        shezhi_cuowu(null);
        
        // 模拟API调用延迟
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 暂时使用模拟数据
        shezhi_guaiwushuju(moniGuaiwushuju);
        
      } catch (error) {
        console.error('加载怪物数据失败:', error);
        shezhi_cuowu('加载怪物数据失败，请稍后重试');
      } finally {
        shezhi_jiazaizhong(false);
      }
    };

    jiazaiguaiwushuju();
  }, []);

  // 组件卸载时清理动画
  useEffect(() => {
    return () => {
      if (gundongdonghuaref.current) {
        cancelAnimationFrame(gundongdonghuaref.current);
      }
    };
  }, []);

  // 添加原生滚轮事件监听器
  useEffect(() => {
    const rongqi = gundongrongqiref.current;
    if (!rongqi) return;

    const chuliyuanshenggunlun = (event) => {
      const zhichi = zhichishubiaogundongg();

      if (!zhichi || !shubiaoruyu) return;

      const gundonglianggg = event.deltaY * 2;
      const dangqianweizhii = rongqi.scrollLeft;
      const zuida_gundong = rongqi.scrollWidth - rongqi.clientWidth;
      const xinweizhii = Math.max(0, Math.min(zuida_gundong, dangqianweizhii + gundonglianggg));

      if (zuida_gundong > 0) {
        event.preventDefault();
        pinghuagundong(xinweizhii, 150);
      }
    };

    rongqi.addEventListener('wheel', chuliyuanshenggunlun, { passive: false });

    return () => {
      rongqi.removeEventListener('wheel', chuliyuanshenggunlun);
    };
  }, [shubiaoruyu]);

  // 处理怪物卡片点击
  const chulidianjikapian = (guaiwu) => {
    console.log('点击怪物:', guaiwu);
    // 这里可以添加跳转到怪物详情页的逻辑
  };

  return (
    <Guaiwuliebiaorongqi>
      {/* 标题区域 */}
      <Biaotiquyu>
        <Guaiwutubiao><span>👾</span></Guaiwutubiao>
        <Biaotiwenzi>怪物数据</Biaotiwenzi>
      </Biaotiquyu>

      {/* 内容区域 */}
      {jiazaizhong ? (
        <Jiazaizhuangtai>正在加载怪物数据...</Jiazaizhuangtai>
      ) : cuowu ? (
        <Cuowuzhuangtai>{cuowu}</Cuowuzhuangtai>
      ) : (
        <Guaiwugundongrongqi
          ref={gundongrongqiref}
          onMouseEnter={chulishubiaoruyu}
          onMouseLeave={chulishubiaolikai}
        >
          <Guaiwuliebiaoliebiao>
            <AnimatePresence>
              {guaiwushuju.map((guaiwu, suoyin) => (
                <Guaiwukapian
                  key={guaiwu.guaiwu_id}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3, delay: suoyin * 0.1 }}
                  onClick={() => chulidianjikapian(guaiwu)}
                >
                  <Guaiwutouxiang>
                    {huoquguaiwubiaoqing(guaiwu.guaiwu_mingcheng)}
                  </Guaiwutouxiang>
                  <Guaiwumingcheng>{guaiwu.guaiwu_mingcheng}</Guaiwumingcheng>
                  <Guaiwudengji>Lv.{guaiwu.level}</Guaiwudengji>
                </Guaiwukapian>
              ))}
            </AnimatePresence>
          </Guaiwuliebiaoliebiao>
        </Guaiwugundongrongqi>
      )}
    </Guaiwuliebiaorongqi>
  );
}

export default Guaiwuliebiao;
