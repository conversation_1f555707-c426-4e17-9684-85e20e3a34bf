[package]
name = "wang<PERSON><PERSON><PERSON><PERSON><PERSON>"
version = "0.1.0"
edition = "2021"
authors = ["yunluo345 <<EMAIL>>"]
description = "WSAM网络请求处理模块 - WebAssembly库"

[lib]
crate-type = ["cdylib"]

[dependencies]
wasm-bindgen = { version = "0.2", features = ["serde-serialize"] }
wasm-bindgen-futures = "0.4"
js-sys = "0.3"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
serde-wasm-bindgen = "0.4"


[dependencies.web-sys]
version = "0.3"
features = [
  "console",
  "Request",
  "RequestInit",
  "RequestMode",
  "Response",
  "Window",
  "Headers",
  "AbortController",
  "AbortSignal",
]
