use wasm_bindgen::prelude::*;
use std::sync::Mutex;

static wangguan_dizhi: Mutex<Option<String>> = Mutex::new(None);

#[wasm_bindgen]
pub fn chushihuawangguan(wangguan_dizhi_canshu: String) {
    let mut dizhi = wangguan_dizhi.lock().unwrap();
    *dizhi = Some(wangguan_dizhi_canshu);
}

pub fn huoquwangguandizhi() -> Option<String> {
    let dizhi = wangguan_dizhi.lock().unwrap();
    dizhi.clone()
}

pub fn goujianwanzhengurl(luyou: &str) -> Result<String, String> {
    match huoquwangguandizhi() {
        Some(wangguan) => {
            let wanzheng_url = if luyou.starts_with('/') {
                format!("{}{}", wangguan, luyou)
            } else {
                format!("{}/{}", wangguan, luyou)
            };
            Ok(wanzheng_url)
        },
        None => Err("网关未初始化，请先调用chushihuawangguan函数".to_string()),
    }
}
