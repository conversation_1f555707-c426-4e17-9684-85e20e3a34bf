use wasm_bindgen::prelude::*;
use wasm_bindgen_futures::JsFuture;
use web_sys::{Request, RequestInit, RequestMode, Response, Headers, AbortController};
use js_sys::Promise;
use crate::wangguanquanjuguanli::goujianwanzhengurl;

async fn zhixingqingqiudaichongshi(
    request: Request,
    chaoshishijian: Option<u32>,
    chongshicishu: Option<u32>,
) -> Result<JsValue, JsValue> {
    let zuida_chongshi = chongshicishu.unwrap_or(0);
    let _chaoshi_haomiao = chaoshishijian.unwrap_or(30000); // 默认30秒超时

    for dangqian_cishu in 0..=zuida_chongshi {
        // 克隆请求对象
        let cloned_request = request.clone().unwrap();

        // 简化版本：直接发送请求，不使用复杂的超时机制
        let window = web_sys::window().unwrap();
        let fetch_promise = window.fetch_with_request(&cloned_request);
        let result = JsFuture::from(fetch_promise).await;

        match result {
            Ok(resp_value) => {
                let resp: Response = resp_value.dyn_into().unwrap();
                if resp.ok() {
                    let json = JsFuture::from(resp.json()?).await?;
                    return Ok(json);
                } else if dangqian_cishu == zuida_chongshi {
                    return Err(JsValue::from_str(&format!("请求失败，状态码: {}", resp.status())));
                }
            },
            Err(e) => {
                if dangqian_cishu == zuida_chongshi {
                    return Err(e);
                }
            }
        }

        // 如果不是最后一次重试，等待一段时间再重试
        if dangqian_cishu < zuida_chongshi {
            let delay = (dangqian_cishu + 1) * 1000; // 递增延迟：1秒、2秒、3秒...
            let promise = Promise::new(&mut |resolve, _| {
                let window = web_sys::window().unwrap();
                let callback = Closure::wrap(Box::new(move || {
                    resolve.call0(&JsValue::NULL).unwrap();
                }) as Box<dyn FnMut()>);
                window.set_timeout_with_callback_and_timeout_and_arguments_0(
                    callback.as_ref().unchecked_ref(),
                    delay as i32,
                ).unwrap();
                callback.forget();
            });
            JsFuture::from(promise).await.unwrap();
        }
    }

    Err(JsValue::from_str("所有重试都失败了"))
}

#[wasm_bindgen]
pub async fn get_qingqiu(
    luyou: String,
    canshu: Option<String>,
    _jiemi_xiangying: bool,
    _jiami_qingqiu: bool,
    chaoshishijian: Option<u32>,
    chongshicishu: Option<u32>,
) -> Result<JsValue, JsValue> {
    let wanzheng_url = match goujianwanzhengurl(&luyou) {
        Ok(url) => url,
        Err(e) => return Err(JsValue::from_str(&e)),
    };

    let zuizhong_url = if let Some(params) = canshu {
        if wanzheng_url.contains('?') {
            format!("{}&{}", wanzheng_url, params)
        } else {
            format!("{}?{}", wanzheng_url, params)
        }
    } else {
        wanzheng_url
    };

    // 目前加密解密功能暂时注释
    // if jiami_qingqiu {
    //     // 这里将来实现请求加密
    // }

    let opts = RequestInit::new();
    opts.set_method("GET");
    opts.set_mode(RequestMode::Cors);

    let request = Request::new_with_str_and_init(&zuizhong_url, &opts)
        .map_err(|e| JsValue::from_str(&format!("创建请求失败: {:?}", e)))?;

    // 使用带超时和重试的请求执行函数
    let json = zhixingqingqiudaichongshi(request, chaoshishijian, chongshicishu).await?;

    // 目前解密功能暂时注释
    // if jiemi_xiangying {
    //     // 这里将来实现响应解密
    // }

    Ok(json)
}

#[wasm_bindgen]
pub async fn post_qingqiu(
    luyou: String,
    qingqiuti: Option<String>,
    _jiemi_xiangying: bool,
    _jiami_qingqiu: bool,
    chaoshishijian: Option<u32>,
    chongshicishu: Option<u32>,
) -> Result<JsValue, JsValue> {
    let wanzheng_url = match goujianwanzhengurl(&luyou) {
        Ok(url) => url,
        Err(e) => return Err(JsValue::from_str(&e)),
    };

    // 目前加密解密功能暂时注释
    // if jiami_qingqiu {
    //     // 这里将来实现请求加密
    // }

    let opts = RequestInit::new();
    opts.set_method("POST");
    opts.set_mode(RequestMode::Cors);

    let headers = Headers::new().unwrap();
    headers.set("Content-Type", "application/json").unwrap();
    opts.set_headers(&headers);

    if let Some(body) = qingqiuti {
        opts.set_body(&JsValue::from_str(&body));
    }

    let request = Request::new_with_str_and_init(&wanzheng_url, &opts)
        .map_err(|e| JsValue::from_str(&format!("创建请求失败: {:?}", e)))?;

    // 使用带超时和重试的请求执行函数
    let json = zhixingqingqiudaichongshi(request, chaoshishijian, chongshicishu).await?;

    // 目前解密功能暂时注释
    // if jiemi_xiangying {
    //     // 这里将来实现响应解密
    // }

    Ok(json)
}
